# This file was autogenerated by uv via the following command:
#    ./scripts/generate_requirements.sh upgrade
agno==1.8.1
agno-aws==0.0.2
agno-docker==0.0.1
aiofiles==24.1.0
alembic==1.16.5
altair==5.5.0
annotated-types==0.7.0
anyio==4.10.0
attrs==25.3.0
beautifulsoup4==4.13.5
blinker==1.9.0
boto3==1.40.25
botocore==1.40.25
cachetools==6.2.0
certifi==2025.8.3
cffi==1.17.1
charset-normalizer==3.4.3
click==8.2.1
curl-cffi==0.13.0
ddgs==9.5.5
distro==1.9.0
dnspython==2.7.0
docker==7.1.0
docstring-parser==0.17.0
email-validator==2.3.0
exa-py==1.15.5
fastapi==0.116.1
fastapi-cli==0.0.10
fastapi-cloud-cli==0.1.5
feedparser==6.0.11
filelock==3.19.1
frozendict==2.4.6
gitdb==4.0.12
gitpython==3.1.45
greenlet==3.2.4
h11==0.16.0
httpcore==1.0.9
httptools==0.6.4
httpx==0.28.1
idna==3.10
jinja2==3.1.6
jiter==0.10.0
jmespath==1.0.1
joblib==1.5.2
jsonschema==4.25.1
jsonschema-specifications==2025.4.1
lxml==6.0.1
lxml-html-clean==0.4.2
mako==1.3.10
markdown-it-py==4.0.0
markupsafe==3.0.2
mdurl==0.1.2
multitasking==0.0.12
narwhals==2.3.0
nest-asyncio==1.6.0
newspaper4k==*******
nltk==3.9.1
numpy==2.3.2
openai==1.106.1
packaging==25.0
pandas==2.3.2
peewee==3.18.2
pgvector==0.4.1
pillow==11.3.0
platformdirs==4.4.0
primp==0.15.0
protobuf==6.32.0
psycopg==3.2.9
psycopg-binary==3.2.9
pyarrow==21.0.0
pycparser==2.22
pydantic==2.11.7
pydantic-core==2.33.2
pydantic-settings==2.10.1
pydeck==0.9.1
pygments==2.19.2
pypdf==6.0.0
python-dateutil==2.9.0.post0
python-docx==1.2.0
python-dotenv==1.1.1
python-multipart==0.0.20
pytz==2025.2
pyyaml==6.0.2
referencing==0.36.2
regex==2025.9.1
requests==2.32.5
requests-file==2.1.0
rich==14.1.0
rich-toolkit==0.15.1
rignore==0.6.4
rpds-py==0.27.1
s3transfer==0.13.1
sentry-sdk==2.37.0
sgmllib3k==1.0.0
shellingham==1.5.4
six==1.17.0
smmap==5.0.2
sniffio==1.3.1
soupsieve==2.8
sqlalchemy==2.0.43
starlette==0.47.3
streamlit==1.49.1
tenacity==9.1.2
tiktoken==0.11.0
tldextract==5.3.0
toml==0.10.2
tomli==2.2.1
tornado==6.5.2
tqdm==4.67.1
typer==0.17.4
typing-extensions==4.15.0
typing-inspection==0.4.1
tzdata==2025.2
urllib3==2.5.0
uvicorn==0.35.0
uvloop==0.21.0
watchdog==6.0.0
watchfiles==1.1.0
websockets==15.0.1
yfinance==0.2.65

# Image Generation Dependencies
opencv-python==*********
rapidocr-onnxruntime==1.4.4

# MCP (Model Context Protocol) Dependencies
mcp==1.11.0
httpx-sse==0.4.0
sse-starlette==2.4.1
