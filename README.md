## Agent App

This repo contains the code for a production-grade agentic system built with:

1. A Streamlit UI
2. A FastAPI server
3. A Postgres database with the PgVector extension.

### 🛍️ Featured: BigCommerce Customer Service Agent

This app includes a specialized **BigCommerce Customer Service Agent** for Trendy Transfers ecommerce:

- **Real-time Order Management**: Access BigCommerce orders, tracking, and shipping info
- **Product Catalog Integration**: Search and provide information about DTF transfers, screen prints, patches
- **Billing & Payment Support**: Handle payment issues, refunds, and billing inquiries
- **Knowledge Base Integration**: Uses comprehensive FAQ database for consistent policy responses
- **Zendesk Escalation**: Seamlessly escalate complex issues to human support
- **Multi-Agent Architecture**: 5 specialized agents working together for comprehensive support

**Quick Setup for BigCommerce Agent:**

```bash
# Set required environment variables
export OPENAI_API_KEY="your-openai-api-key"
export BIGCOMMERCE_ACCESS_TOKEN="your-bigcommerce-token"
export BIGCOMMERCE_STORE_HASH="your-store-hash"

# Run setup helper
python scripts/setup_bigcommerce_env.py
```

You can run the agent app in 2 environments:

1. A development environment running locally on docker
2. A production environment running on AWS ECS

## Setup

1. [Install uv](https://docs.astral.sh/uv/#getting-started) for managing the python environment.

```bash
curl -LsSf https://astral.sh/uv/install.sh | sh
```

2. Create a virtual environment and install dependencies:

```sh
./scripts/dev_setup.sh
```

3. Activate virtual environment

```
source .venv/bin/activate
```

## Run application locally using docker

1. Install [docker desktop](https://www.docker.com/products/docker-desktop)

2. Export API keys

Required: Set the `OPENAI_API_KEY` environment variable using

```sh
export OPENAI_API_KEY=***
```

> You may use any supported model provider, just need to update the respective Agent, Team or Workflow.

3. Start the workspace:

```sh
ag ws up
```

- This will run 3 containers:
  - Streamlit on [localhost:8501](http://localhost:8501)
  - FastAPI on [localhost:8000](http://localhost:8000/docs)
  - Postgres on  [localhost:5432](http://localhost:5432)
- Open [localhost:8501](http://localhost:8501) to view the Streamlit App.
- Open [localhost:8000/docs](http://localhost:8000/docs) to view the FastAPI docs.

4. Stop the workspace using:

```sh
ag ws down
```

## More Information

Learn more about this application and how to customize it in the [Agno Workspaces](https://docs.agno.com/workspaces) documentaion
