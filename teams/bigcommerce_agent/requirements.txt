# Trendy Transfers BigCommerce Customer Service Agent Requirements

# Core AI Framework
agno>=0.1.0

# Language Models
openai>=1.0.0

# Web Framework and API
fastapi>=0.100.0
uvicorn[standard]>=0.20.0
websockets>=11.0.0

# HTTP Client for API calls
httpx>=0.24.0

# Database and Vector Storage
psycopg[binary]>=3.1.0
pgvector>=0.2.0

# Data Processing
pydantic>=2.0.0

# Optional: For enhanced functionality
# streamlit>=1.28.0  # If you want to add a Streamlit interface
# gradio>=3.40.0     # Alternative web interface option

# Development and Testing (optional)
pytest>=7.0.0
pytest-asyncio>=0.21.0
black>=23.0.0
flake8>=6.0.0

# Environment Management
python-dotenv>=1.0.0
