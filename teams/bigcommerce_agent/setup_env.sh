#!/bin/bash

# Trendy Transfers BigCommerce Customer Service Agent
# Environment Setup Script

echo "🚀 Setting up Trendy Transfers Customer Service Agent Environment"
echo "=================================================================="

# Check if .env file exists
if [ -f ".env" ]; then
    echo "⚠️  .env file already exists. Backing up to .env.backup"
    cp .env .env.backup
fi

# Copy example file
if [ -f ".env.example" ]; then
    cp .env.example .env
    echo "✅ Created .env file from .env.example"
else
    echo "❌ .env.example file not found!"
    exit 1
fi

echo ""
echo "📝 Please edit the .env file and add your credentials:"
echo ""
echo "Required:"
echo "  - OPENAI_API_KEY: Your OpenAI API key"
echo "  - BIGCOMMERCE_ACCESS_TOKEN: Already filled in (ml5lfohky6syd3vpzdfihnrr8kexjvc)"
echo "  - BIGCOMMERCE_STORE_HASH: Already filled in (5fl0unj4d)"
echo ""
echo "Optional (for Zendesk integration):"
echo "  - ZENDESK_SUBDOMAIN: Your Zendesk subdomain"
echo "  - ZENDESK_EMAIL: Your Zendesk admin email"
echo "  - ZENDESK_API_TOKEN: Your Zendesk API token"
echo ""

# Ask if user wants to edit now
read -p "Would you like to edit the .env file now? (y/n): " edit_now

if [ "$edit_now" = "y" ] || [ "$edit_now" = "Y" ]; then
    # Try to open with common editors
    if command -v code &> /dev/null; then
        code .env
    elif command -v nano &> /dev/null; then
        nano .env
    elif command -v vim &> /dev/null; then
        vim .env
    else
        echo "Please edit .env manually with your preferred editor"
    fi
fi

echo ""
echo "🔧 Next steps:"
echo "1. Make sure you've filled in your OPENAI_API_KEY in .env"
echo "2. Install dependencies: pip install -r requirements.txt"
echo "3. Test the setup: python run.py demo"
echo ""
echo "📚 For more information, see README.md"
