"""
Configuration for Trendy Transfers BigCommerce Customer Service Agent

This file contains all the configuration settings for the customer service system.
Update these settings to match your specific setup.
"""

import os
from typing import Optional

class TrendyTransfersConfig:
    """Configuration class for Trendy Transfers customer service system"""
    
    # BigCommerce MCP Server Configuration
    BIGCOMMERCE_MCP_URL = "https://bigcommerce-api-mcp.etugrand.com/mcp"
    
    # Database Configuration
    DATABASE_URL = "postgresql+psycopg://ai:ai@localhost:5532/ai"
    KNOWLEDGE_BASE_TABLE = "trendy_transfers_kb"
    
    # Knowledge Base Sources
    KNOWLEDGE_BASE_SOURCES = [
        "TT/FAQs .md",
        # Add more knowledge sources here:
        # "TT/policies.md",
        # "TT/product_info.md",
    ]
    
    # Zendesk Configuration (optional)
    ZENDESK_SUBDOMAIN = os.getenv("ZENDESK_SUBDOMAIN")  # e.g., "trendytransfers"
    ZENDESK_EMAIL = os.getenv("ZENDESK_EMAIL")  # Your Zendesk admin email
    ZENDESK_API_TOKEN = os.getenv("ZENDESK_API_TOKEN")  # Your Zendesk API token
    
    # OpenAI Configuration
    OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
    DEFAULT_MODEL = "gpt-4o"
    
    # Web Interface Configuration
    WEB_CHAT_HOST = "0.0.0.0"
    WEB_CHAT_PORT = 8001
    
    # Company Information
    COMPANY_NAME = "Trendy Transfers"
    CUSTOMER_SERVICE_EMAIL = "<EMAIL>"
    PROCESSING_TIME = "1-3 business days"
    SHIPPING_LOCATION = "Iowa"
    BUSINESS_DAYS = "Monday through Friday"
    
    # Agent Configuration
    ENABLE_DEBUG_MODE = False
    SHOW_TOOL_CALLS = True
    ENABLE_TEAM_HISTORY = True
    
    @classmethod
    def get_zendesk_config(cls) -> Optional[dict]:
        """Get Zendesk configuration if all required fields are present"""
        if cls.ZENDESK_SUBDOMAIN and cls.ZENDESK_EMAIL and cls.ZENDESK_API_TOKEN:
            return {
                "zendesk_subdomain": cls.ZENDESK_SUBDOMAIN,
                "zendesk_email": cls.ZENDESK_EMAIL,
                "zendesk_api_token": cls.ZENDESK_API_TOKEN
            }
        return None
    
    @classmethod
    def validate_config(cls) -> list[str]:
        """Validate configuration and return list of missing/invalid settings"""
        issues = []
        
        if not cls.OPENAI_API_KEY:
            issues.append("OPENAI_API_KEY environment variable is not set")
        
        if not cls.BIGCOMMERCE_MCP_URL:
            issues.append("BIGCOMMERCE_MCP_URL is not configured")
        
        if not cls.DATABASE_URL:
            issues.append("DATABASE_URL is not configured")
        
        # Check if knowledge base sources exist
        for source in cls.KNOWLEDGE_BASE_SOURCES:
            if not os.path.exists(source):
                issues.append(f"Knowledge base source not found: {source}")
        
        return issues

# Environment-specific configurations
class DevelopmentConfig(TrendyTransfersConfig):
    """Development environment configuration"""
    ENABLE_DEBUG_MODE = True
    WEB_CHAT_HOST = "localhost"

class ProductionConfig(TrendyTransfersConfig):
    """Production environment configuration"""
    ENABLE_DEBUG_MODE = False
    WEB_CHAT_HOST = "0.0.0.0"

class TestingConfig(TrendyTransfersConfig):
    """Testing environment configuration"""
    DATABASE_URL = "postgresql+psycopg://ai:ai@localhost:5532/ai_test"
    KNOWLEDGE_BASE_TABLE = "trendy_transfers_kb_test"
    ENABLE_DEBUG_MODE = True

# Get configuration based on environment
def get_config() -> TrendyTransfersConfig:
    """Get configuration based on environment variable"""
    env = os.getenv("ENVIRONMENT", "development").lower()
    
    if env == "production":
        return ProductionConfig()
    elif env == "testing":
        return TestingConfig()
    else:
        return DevelopmentConfig()

# Default configuration instance
config = get_config()
