"""
BigCommerce Customer Service Team for Trendy Transfers

This module contains the main customer service team with specialized agents
for handling different types of customer inquiries.
"""

import asyncio
from typing import Optional
from agno.agent import Agent
from agno.models.openai import OpenAIChat
from agno.team.team import Team
from agno.tools.mcp import MCPTools
from agno.knowledge.text import TextKnowledgeBase
from agno.vectordb.pgvector import PgVector
from .zendesk_integration import TrendyTransfersZendesk

# Configuration
BIGCOMMERCE_MCP_URL = "https://bigcommerce-api-mcp.etugrand.com/mcp"

class BigCommerceCustomerServiceTeam:
    """
    Main customer service team for BigCommerce store support.
    
    Features:
    - Intelligent routing to specialized agents
    - Real-time BigCommerce data access
    - Knowledge base integration
    - Zendesk escalation capabilities
    """
    
    def __init__(
        self,
        bigcommerce_mcp_url: str = BIGCOMMERCE_MCP_URL,
        knowledge_base_sources: list[str] = None,
        db_url: str = "postgresql+psycopg://ai:ai@localhost:5532/ai",
        zendesk_config: dict = None
    ):
        self.bigcommerce_mcp_url = bigcommerce_mcp_url
        self.db_url = db_url
        self.mcp_tools = None
        self.team = None
        self.zendesk = None
        
        # Setup Zendesk integration if config provided
        if zendesk_config:
            self.zendesk = TrendyTransfersZendesk(**zendesk_config)
        
        # Setup knowledge base
        self.knowledge_base = TextKnowledgeBase(
            sources=knowledge_base_sources or ["TT/FAQs .md"],
            vector_db=PgVector(
                table_name="trendy_transfers_kb",
                db_url=db_url,
            ),
        )
        
        self._setup_agents()
    
    def _setup_agents(self):
        """Create specialized customer service agents"""
        
        # Order Management Agent
        self.order_agent = Agent(
            name="Order Management Specialist",
            role="Handle order inquiries, tracking, and status updates",
            model=OpenAIChat(id="gpt-4o"),
            knowledge=self.knowledge_base,
            search_knowledge=True,
            instructions=[
                "You are an order management specialist for Trendy Transfers.",
                "Help customers with order status, tracking, modifications, and general order questions.",
                "",
                "Key Information:",
                "- Processing time: 1-3 business days",
                "- Orders ship from Iowa",
                "- No weekend or holiday processing (Monday-Friday only)",
                "- Use BigCommerce tools to check real-time order information",
                "",
                "Guidelines:",
                "- Always check the BigCommerce system for current order status",
                "- Provide specific order numbers and tracking information",
                "- Be empathetic about delays and explain processing times clearly",
                "- If orders aren't showing in customer accounts, note for manual linking",
                "- Escalate complex shipping issues or damaged items",
                "",
                "When using BigCommerce tools:",
                "- Use get_all_orders to find order information by customer email or order ID",
                "- Look for order status, shipping details, and tracking numbers",
                "- Check order dates to calculate expected processing/shipping times",
            ],
            markdown=True,
        )
        
        # Product Information Agent  
        self.product_agent = Agent(
            name="Product Information Specialist",
            role="Handle product questions, pricing, and availability",
            model=OpenAIChat(id="gpt-4o"),
            knowledge=self.knowledge_base,
            search_knowledge=True,
            instructions=[
                "You are a product specialist for Trendy Transfers.",
                "Help customers with product information, pricing, availability, and recommendations.",
                "",
                "Guidelines:",
                "- Use BigCommerce tools to get current product details and inventory",
                "- Provide accurate pricing and availability information",
                "- Suggest related or alternative products when appropriate",
                "- Be knowledgeable about product features, sizing, and customization",
                "- Help customers find the right products for their needs",
                "",
                "When using BigCommerce tools:",
                "- Use get_all_products to search for products by name, category, or SKU",
                "- Check current inventory levels and pricing",
                "- Look for product variants, options, and customization details",
            ],
            markdown=True,
        )
        
        # Customer Account Agent
        self.account_agent = Agent(
            name="Customer Account Specialist", 
            role="Handle account-related questions and customer information",
            model=OpenAIChat(id="gpt-4o"),
            knowledge=self.knowledge_base,
            search_knowledge=True,
            instructions=[
                "You are a customer account specialist for Trendy Transfers.",
                "Help customers with account access, profile updates, and account-related questions.",
                "",
                "Guidelines:",
                "- Use BigCommerce customer data to assist with account issues",
                "- Guide customers through password resets and login processes",
                "- Help with profile updates and account information changes",
                "- Protect customer privacy - verify identity before sharing details",
                "- Note when orders need manual linking to customer accounts",
                "",
                "When using BigCommerce tools:",
                "- Use get_all_customers to find customer information by email",
                "- Check customer order history and account details",
                "- Verify customer identity before providing sensitive information",
            ],
            markdown=True,
        )
        
        # Billing and Payment Agent
        self.billing_agent = Agent(
            name="Billing and Payment Specialist",
            role="Handle billing questions, payment issues, and refunds", 
            model=OpenAIChat(id="gpt-4o"),
            knowledge=self.knowledge_base,
            search_knowledge=True,
            instructions=[
                "You are a billing specialist for Trendy Transfers.",
                "Help customers with payment questions, billing issues, and refund requests.",
                "",
                "Guidelines:",
                "- Be understanding and empathetic about billing concerns",
                "- Explain charges clearly and help customers understand invoices",
                "- Guide customers through payment retry processes",
                "- Follow company refund policies (check knowledge base)",
                "- Escalate refund requests that require manual processing",
                "",
                "When using BigCommerce tools:",
                "- Use order information to verify billing details",
                "- Check payment status and transaction information",
                "- Look for duplicate charges or billing discrepancies",
            ],
            markdown=True,
        )
        
        # Escalation Manager
        self.escalation_agent = Agent(
            name="Support Escalation Manager",
            role="Handle escalations and create support tickets",
            model=OpenAIChat(id="gpt-4o"),
            knowledge=self.knowledge_base,
            search_knowledge=True,
            instructions=[
                "You are the escalation manager for Trendy Transfers customer support.",
                "Handle issues that cannot be resolved by other agents.",
                "",
                "Guidelines:",
                "- Gather all relevant information before escalating",
                "- Create detailed Zendesk tickets with customer information",
                "- Provide customers with ticket numbers and response expectations",
                "- Be professional and reassuring during escalations",
                "- Follow up on escalated issues when possible",
                "",
                "Escalation triggers:",
                "- Complex technical issues",
                "- Refund requests requiring approval",
                "- Shipping problems or damaged items",
                "- Account access issues that can't be resolved",
                "- Customer complaints requiring human intervention",
                "",
                "When escalating:",
                "- Use Zendesk integration to create tickets",
                "- Include all conversation context and customer details",
                "- Set appropriate priority based on issue severity",
                "- Provide customer with ticket number and next steps",
            ],
            markdown=True,
        )
    
    async def initialize(self):
        """Initialize the customer service team"""
        
        # Initialize BigCommerce MCP tools
        self.mcp_tools = MCPTools(
            url=self.bigcommerce_mcp_url,
            transport="streamable-http"
        )
        await self.mcp_tools.connect()
        
        # Add MCP tools to relevant agents
        for agent in [self.order_agent, self.product_agent, self.account_agent, self.billing_agent]:
            agent.tools = [self.mcp_tools]
        
        # Add Zendesk tools to escalation agent if available
        if self.zendesk:
            self.escalation_agent.tools = [self.zendesk.zendesk]
        
        # Load knowledge base
        try:
            self.knowledge_base.load(recreate=False)
        except:
            print("Loading knowledge base for first time...")
            self.knowledge_base.load(recreate=True)
        
        # Create the main customer service team
        self.team = Team(
            name="Trendy Transfers Customer Service",
            mode="route",
            model=OpenAIChat(id="gpt-4o"),
            enable_team_history=True,
            members=[
                self.order_agent,
                self.product_agent, 
                self.account_agent,
                self.billing_agent,
                self.escalation_agent
            ],
            show_tool_calls=True,
            markdown=True,
            debug_mode=False,
            show_members_responses=True,
            instructions=[
                "You are the lead customer service agent for Trendy Transfers.",
                "Analyze customer inquiries and route them to the appropriate specialist:",
                "",
                "ROUTING GUIDELINES:",
                "🔹 Order questions (status, tracking, shipping) → Order Management Specialist",
                "🔹 Product questions (info, pricing, availability) → Product Information Specialist",
                "🔹 Account issues (login, profile, access) → Customer Account Specialist", 
                "🔹 Billing/payment questions → Billing and Payment Specialist",
                "🔹 Complex issues needing human help → Support Escalation Manager",
                "",
                "CUSTOMER SERVICE STANDARDS:",
                "- Always be friendly, professional, and helpful",
                "- Provide clear explanations for routing decisions",
                "- Ensure seamless handoffs between specialists",
                "- Maintain conversation context throughout",
                "- Ask clarifying questions if routing is unclear",
                "",
                "COMPANY INFO:",
                "- Processing time: 1-3 business days",
                "- Ships from Iowa, Monday-Friday only",
                "- Customer service: <EMAIL>",
            ],
        )
    
    async def handle_query(self, query: str, customer_email: str = None) -> str:
        """
        Handle a customer service query
        
        Args:
            query: Customer's question or issue
            customer_email: Customer's email for context (optional)
            
        Returns:
            Response from the appropriate agent
        """
        if not self.team:
            await self.initialize()
        
        # Add customer context if email provided
        if customer_email:
            context_query = f"Customer Email: {customer_email}\n\nQuery: {query}"
        else:
            context_query = query
            
        response = await self.team.arun(context_query)
        return response.content
    
    async def close(self):
        """Clean up resources"""
        if self.mcp_tools:
            await self.mcp_tools.close()
        if self.zendesk:
            await self.zendesk.close()

# Factory function for easy setup
async def create_customer_service_team(
    bigcommerce_mcp_url: str = BIGCOMMERCE_MCP_URL,
    knowledge_sources: list[str] = None,
    zendesk_config: dict = None
) -> BigCommerceCustomerServiceTeam:
    """
    Create and initialize a BigCommerce customer service team
    
    Args:
        bigcommerce_mcp_url: URL of your BigCommerce MCP server
        knowledge_sources: List of knowledge base source files
        zendesk_config: Dict with zendesk_subdomain, zendesk_email, zendesk_api_token
        
    Returns:
        Initialized BigCommerceCustomerServiceTeam
    """
    team = BigCommerceCustomerServiceTeam(
        bigcommerce_mcp_url=bigcommerce_mcp_url,
        knowledge_base_sources=knowledge_sources,
        zendesk_config=zendesk_config
    )
    await team.initialize()
    return team
