"""
Web Chat Interface for Trendy Transfers Customer Service

This creates a lightweight, embeddable chat interface that can be placed
in the corner of your website without being intrusive.
"""

import asyncio
from fastapi import FastAP<PERSON>, WebSocket, WebSocketDisconnect
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse
from agno.agent import Agent
from agno.models.openai import OpenAIChat
from agno.app.agui.app import AG<PERSON>App
from .customer_service_team import create_customer_service_team
import json
import uvicorn

# Configuration
BIGCOMMERCE_MCP_URL = "https://bigcommerce-api-mcp.etugrand.com/mcp"

app = FastAPI(title="Trendy Transfers Customer Support Chat")

class ChatManager:
    def __init__(self):
        self.customer_service_team = None
    
    async def initialize(self):
        """Initialize the customer service team"""
        if not self.customer_service_team:
            self.customer_service_team = await create_customer_service_team(
                bigcommerce_mcp_url=BIGCOMMERCE_MCP_URL,
                knowledge_sources=["teams/bigcommerce_agent/data/faqs.md"],
                # Add Zendesk config here if available:
                # zendesk_config={
                #     "zendesk_subdomain": "your-subdomain",
                #     "zendesk_email": "<EMAIL>", 
                #     "zendesk_api_token": "your-api-token"
                # }
            )
    
    async def chat(self, message: str, customer_email: str = None) -> str:
        """Process a chat message and return response"""
        if not self.customer_service_team:
            await self.initialize()
        
        try:
            response = await self.customer_service_team.handle_query(message, customer_email)
            return response
        except Exception as e:
            return f"I apologize, but I'm experiencing technical difficulties. Please contact our support <NAME_EMAIL> or try again in a moment. Error: {str(e)}"
    
    async def close(self):
        """Clean up resources"""
        if self.customer_service_team:
            await self.customer_service_team.close()

# Global chat manager instance
chat_manager = ChatManager()

@app.on_event("startup")
async def startup_event():
    """Initialize the chat manager on startup"""
    await chat_manager.initialize()

@app.on_event("shutdown")
async def shutdown_event():
    """Clean up on shutdown"""
    await chat_manager.close()

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket endpoint for real-time chat"""
    await websocket.accept()
    
    try:
        while True:
            # Receive message from client
            data = await websocket.receive_text()
            message_data = json.loads(data)
            user_message = message_data.get("message", "")
            customer_email = message_data.get("email", None)
            
            if user_message:
                # Process with chat manager
                response = await chat_manager.chat(user_message, customer_email)
                
                # Send response back to client
                await websocket.send_text(json.dumps({
                    "type": "response",
                    "message": response
                }))
    
    except WebSocketDisconnect:
        print("Client disconnected")
    except Exception as e:
        print(f"WebSocket error: {e}")
        await websocket.send_text(json.dumps({
            "type": "error",
            "message": "Sorry, something went wrong. Please try again."
        }))

@app.get("/")
async def get_chat_interface():
    """Serve the chat interface HTML"""
    return HTMLResponse(content="""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trendy Transfers Support</title>
    <style>
        /* Chat widget styles */
        #chat-widget {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1000;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        #chat-button {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }
        
        #chat-button:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(0,0,0,0.2);
        }
        
        #chat-button svg {
            width: 24px;
            height: 24px;
            fill: white;
        }
        
        #chat-window {
            position: absolute;
            bottom: 80px;
            right: 0;
            width: 350px;
            height: 500px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 30px rgba(0,0,0,0.12);
            display: none;
            flex-direction: column;
            overflow: hidden;
        }
        
        #chat-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 16px;
            font-weight: 600;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        #close-chat {
            background: none;
            border: none;
            color: white;
            cursor: pointer;
            font-size: 18px;
            padding: 0;
            width: 24px;
            height: 24px;
        }
        
        #chat-messages {
            flex: 1;
            padding: 16px;
            overflow-y: auto;
            background: #f8f9fa;
        }
        
        .message {
            margin-bottom: 12px;
            max-width: 80%;
        }
        
        .user-message {
            background: #007bff;
            color: white;
            padding: 8px 12px;
            border-radius: 18px 18px 4px 18px;
            margin-left: auto;
            text-align: right;
        }
        
        .bot-message {
            background: white;
            color: #333;
            padding: 8px 12px;
            border-radius: 18px 18px 18px 4px;
            border: 1px solid #e9ecef;
        }
        
        #chat-input-container {
            padding: 16px;
            border-top: 1px solid #e9ecef;
            background: white;
        }
        
        #chat-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 20px;
            outline: none;
            font-size: 14px;
        }
        
        #chat-input:focus {
            border-color: #667eea;
        }
        
        .typing-indicator {
            display: none;
            padding: 8px 12px;
            background: white;
            border-radius: 18px 18px 18px 4px;
            border: 1px solid #e9ecef;
            margin-bottom: 12px;
        }
        
        .typing-dots {
            display: inline-block;
        }
        
        .typing-dots span {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: #999;
            margin: 0 1px;
            animation: typing 1.4s infinite ease-in-out;
        }
        
        .typing-dots span:nth-child(1) { animation-delay: -0.32s; }
        .typing-dots span:nth-child(2) { animation-delay: -0.16s; }
        
        @keyframes typing {
            0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
            40% { transform: scale(1); opacity: 1; }
        }
        
        @media (max-width: 480px) {
            #chat-window {
                width: calc(100vw - 40px);
                height: calc(100vh - 100px);
                bottom: 80px;
                right: 20px;
            }
        }
    </style>
</head>
<body>
    <div id="chat-widget">
        <button id="chat-button">
            <svg viewBox="0 0 24 24">
                <path d="M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h4l4 4 4-4h4c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z"/>
            </svg>
        </button>
        
        <div id="chat-window">
            <div id="chat-header">
                <span>Trendy Transfers Support</span>
                <button id="close-chat">&times;</button>
            </div>
            
            <div id="chat-messages">
                <div class="message bot-message">
                    Hi! I'm here to help with your Trendy Transfers questions. I can assist with orders, products, billing, and account issues. How can I help you today?
                </div>
            </div>
            
            <div class="typing-indicator">
                <div class="typing-dots">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>
            
            <div id="chat-input-container">
                <input type="text" id="chat-input" placeholder="Type your message..." />
            </div>
        </div>
    </div>

    <script>
        class ChatWidget {
            constructor() {
                this.ws = null;
                this.isOpen = false;
                this.customerEmail = null;
                this.initializeElements();
                this.setupEventListeners();
                this.connectWebSocket();
            }
            
            initializeElements() {
                this.chatButton = document.getElementById('chat-button');
                this.chatWindow = document.getElementById('chat-window');
                this.closeButton = document.getElementById('close-chat');
                this.chatInput = document.getElementById('chat-input');
                this.chatMessages = document.getElementById('chat-messages');
                this.typingIndicator = document.querySelector('.typing-indicator');
            }
            
            setupEventListeners() {
                this.chatButton.addEventListener('click', () => this.toggleChat());
                this.closeButton.addEventListener('click', () => this.closeChat());
                this.chatInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') this.sendMessage();
                });
            }
            
            connectWebSocket() {
                const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
                this.ws = new WebSocket(`${protocol}//${window.location.host}/ws`);
                
                this.ws.onmessage = (event) => {
                    const data = JSON.parse(event.data);
                    this.hideTyping();
                    this.addMessage(data.message, 'bot');
                };
                
                this.ws.onerror = () => {
                    this.hideTyping();
                    this.addMessage('Sorry, I\'m having connection issues. Please try again <NAME_EMAIL>', 'bot');
                };
            }
            
            toggleChat() {
                if (this.isOpen) {
                    this.closeChat();
                } else {
                    this.openChat();
                }
            }
            
            openChat() {
                this.chatWindow.style.display = 'flex';
                this.isOpen = true;
                this.chatInput.focus();
            }
            
            closeChat() {
                this.chatWindow.style.display = 'none';
                this.isOpen = false;
            }
            
            sendMessage() {
                const message = this.chatInput.value.trim();
                if (!message) return;
                
                this.addMessage(message, 'user');
                this.chatInput.value = '';
                this.showTyping();
                
                if (this.ws && this.ws.readyState === WebSocket.OPEN) {
                    this.ws.send(JSON.stringify({ 
                        message,
                        email: this.customerEmail 
                    }));
                } else {
                    this.hideTyping();
                    this.addMessage('Connection lost. Please refresh the page <NAME_EMAIL>', 'bot');
                }
            }
            
            addMessage(text, sender) {
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${sender}-message`;
                messageDiv.innerHTML = text; // Allow HTML for formatted responses
                this.chatMessages.appendChild(messageDiv);
                this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
            }
            
            showTyping() {
                this.typingIndicator.style.display = 'block';
                this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
            }
            
            hideTyping() {
                this.typingIndicator.style.display = 'none';
            }
        }
        
        // Initialize chat widget when page loads
        document.addEventListener('DOMContentLoaded', () => {
            new ChatWidget();
        });
    </script>
</body>
</html>
    """)

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "service": "Trendy Transfers Customer Support"}

def create_web_chat_app():
    """Create the web chat application"""
    return app

# AGUI App for development/testing
def create_agui_app():
    """Create an AGUI app for testing the customer service agent"""
    
    # Simple agent for AGUI interface
    agent = Agent(
        model=OpenAIChat(id="gpt-4o"),
        description="Trendy Transfers Customer Service Agent - helping with orders, products, and account questions",
        instructions=[
            "You are a customer service representative for Trendy Transfers.",
            "Help customers with their questions about orders, products, billing, and account issues.",
            "Be friendly, professional, and helpful at all times.",
            "Processing time is 1-3 business days, orders ship from Iowa, Monday-Friday only.",
            "Customer service email: <EMAIL>",
        ],
        markdown=True,
    )
    
    # Create AGUI app
    agui_app = AGUIApp(
        agent=agent,
        name="Trendy Transfers Support",
        app_id="trendy_transfers_support",
        description="24/7 Customer Service for Trendy Transfers - Orders, Products, Billing & More",
    )
    
    return agui_app

if __name__ == "__main__":
    uvicorn.run(
        "web_interface:app",
        host="0.0.0.0",
        port=8001,
        reload=True
    )
