# Trendy Transfers BigCommerce Customer Service Agent Environment Variables
# Copy this file to .env and fill in your actual values

# OpenAI Configuration (Required)
OPENAI_API_KEY=your-openai-api-key-here

# BigCommerce API Configuration (Required)
# From your BigCommerce API credentials file:
BIGCOMMERCE_ACCESS_TOKEN=
BIGCOMMERCE_CLIENT_ID=
BIGCOMMERCE_CLIENT_SECRET=
BIGCOMMERCE_STORE_HASH=
BIGCOMMERCE_API_PATH=https://api.bigcommerce.com/stores/[store_hash]/v3/

# Zendesk Configuration (Optional - for ticket escalation)
ZENDESK_SUBDOMAIN=your-zendesk-subdomain
ZENDESK_EMAIL=<EMAIL>
ZENDESK_API_TOKEN=your-zendesk-api-token

# Environment (Optional - defaults to development)
ENVIRONMENT=development

# Database Configuration (Optional - uses default if not set)
# DATABASE_URL=postgresql+psycopg://ai:ai@localhost:5532/ai
