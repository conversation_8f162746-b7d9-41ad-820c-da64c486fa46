"""
BigCommerce Customer Service Agent Team

A comprehensive customer service solution for BigCommerce stores that provides:
- 24/7 automated customer support
- Real-time order, product, and customer data access
- Intelligent routing to specialized agents
- Zendesk integration for escalation
- Knowledge base integration for FAQs and policies

Components:
- Customer Service Team (main routing agent)
- Specialized agents for orders, products, billing, accounts
- BigCommerce MCP integration
- Zendesk escalation system
- Web chat interface
"""

from .customer_service_team import BigCommerceCustomerServiceTeam
from .web_interface import create_web_chat_app
from .zendesk_integration import TrendyTransfersZendesk

__all__ = [
    "BigCommerceCustomerServiceTeam",
    "create_web_chat_app", 
    "TrendyTransfersZendesk"
]
