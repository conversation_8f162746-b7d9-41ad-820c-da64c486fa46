#!/usr/bin/env python3
"""
Trendy Transfers Customer Service Agent Launcher

This script provides easy ways to run the customer service system:
- Demo mode: Test with sample queries
- Interactive mode: Chat directly with the agent
- Web mode: Start the web chat interface
- AGUI mode: Start the AGUI development interface
"""

import asyncio
import sys
import os
import argparse
import uvicorn

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from demo import demo_customer_service, interactive_demo
from web_interface import create_web_chat_app, create_agui_app
from config import config

def print_banner():
    """Print the application banner"""
    print("""
╔══════════════════════════════════════════════════════════════╗
║                 Trendy Transfers                             ║
║              Customer Service Agent                          ║
║                                                              ║
║  🤖 AI-Powered 24/7 Customer Support                        ║
║  📦 BigCommerce Integration                                  ║
║  🎫 Zendesk Escalation                                       ║
║  💬 Web Chat Interface                                       ║
╚══════════════════════════════════════════════════════════════╝
    """)

def validate_setup():
    """Validate the setup and show any configuration issues"""
    print("🔍 Validating configuration...")
    
    issues = config.validate_config()
    if issues:
        print("❌ Configuration Issues Found:")
        for issue in issues:
            print(f"   • {issue}")
        print("\n💡 Please fix these issues before running the agent.")
        return False
    
    print("✅ Configuration looks good!")
    
    # Show optional features status
    if config.get_zendesk_config():
        print("✅ Zendesk integration: Enabled")
    else:
        print("⚠️  Zendesk integration: Disabled (optional)")
    
    return True

def run_demo():
    """Run the demo with sample queries"""
    print("🚀 Running demo with sample customer service scenarios...")
    asyncio.run(demo_customer_service())

def run_interactive():
    """Run interactive mode"""
    print("🚀 Starting interactive customer service chat...")
    asyncio.run(interactive_demo())

def run_web_chat(host: str = None, port: int = None):
    """Run the web chat interface"""
    host = host or config.WEB_CHAT_HOST
    port = port or config.WEB_CHAT_PORT
    
    print(f"🚀 Starting web chat interface...")
    print(f"🌐 Chat will be available at: http://{host}:{port}")
    print("💡 Embed this chat widget in your website!")
    
    app = create_web_chat_app()
    uvicorn.run(app, host=host, port=port, reload=True)

def run_agui(host: str = "localhost", port: int = 8000):
    """Run the AGUI development interface"""
    print(f"🚀 Starting AGUI development interface...")
    print(f"🌐 Interface will be available at: http://{host}:{port}")
    
    agui_app = create_agui_app()
    agui_app.serve(host=host, port=port, reload=True)

def main():
    """Main function with command line argument parsing"""
    parser = argparse.ArgumentParser(
        description="Trendy Transfers Customer Service Agent",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python run.py demo              # Run demo with sample queries
  python run.py interactive       # Interactive chat mode
  python run.py web              # Start web chat interface
  python run.py web --port 8080  # Start web chat on custom port
  python run.py agui             # Start AGUI development interface
        """
    )
    
    parser.add_argument(
        "mode",
        choices=["demo", "interactive", "web", "agui"],
        help="Mode to run the customer service agent"
    )
    
    parser.add_argument(
        "--host",
        default=None,
        help="Host to bind to (for web/agui modes)"
    )
    
    parser.add_argument(
        "--port",
        type=int,
        default=None,
        help="Port to bind to (for web/agui modes)"
    )
    
    parser.add_argument(
        "--skip-validation",
        action="store_true",
        help="Skip configuration validation"
    )
    
    args = parser.parse_args()
    
    # Print banner
    print_banner()
    
    # Validate setup unless skipped
    if not args.skip_validation:
        if not validate_setup():
            sys.exit(1)
        print()
    
    # Run the selected mode
    try:
        if args.mode == "demo":
            run_demo()
        elif args.mode == "interactive":
            run_interactive()
        elif args.mode == "web":
            run_web_chat(args.host, args.port)
        elif args.mode == "agui":
            agui_port = args.port or 8000
            agui_host = args.host or "localhost"
            run_agui(agui_host, agui_port)
    
    except KeyboardInterrupt:
        print("\n👋 Goodbye! Thanks for using Trendy Transfers Customer Service!")
    except Exception as e:
        print(f"\n❌ Error: {str(e)}")
        if config.ENABLE_DEBUG_MODE:
            import traceback
            traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
