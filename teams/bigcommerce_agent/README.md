# Trendy Transfers BigCommerce Customer Service Agent

A comprehensive AI-powered customer service solution for BigCommerce stores that provides 24/7 automated support with intelligent routing, real-time data access, and human escalation capabilities.

## 🌟 Features

- **🤖 Intelligent Agent Routing**: Automatically routes customer inquiries to specialized agents
- **📦 Real-time BigCommerce Integration**: Access to orders, products, and customer data via MCP server
- **📚 Knowledge Base Integration**: FAQ and policy integration for consistent responses
- **🎫 Zendesk Escalation**: Seamless escalation to human support when needed
- **💬 Web Chat Interface**: Embeddable, non-intrusive chat widget for your website
- **🔧 Specialized Agents**: Dedicated agents for orders, products, billing, accounts, and escalation

## 🏗️ Architecture

```
Customer Query
     ↓
Lead Customer Service Agent (Router)
     ↓
┌─────────────────────────────────────────────────────┐
│  Specialized Agents:                                │
│  • Order Management Specialist                     │
│  • Product Information Specialist                  │
│  • Customer Account Specialist                     │
│  • Billing and Payment Specialist                  │
│  • Support Escalation Manager                      │
└─────────────────────────────────────────────────────┘
     ↓
BigCommerce MCP Server ← → Knowledge Base ← → Zendesk
```

## 🚀 Quick Start

### Prerequisites

1. **Python 3.8+**
2. **OpenAI API Key**
3. **BigCommerce MCP Server** (running at your URL)
4. **PostgreSQL Database** (for knowledge base)
5. **Zendesk Account** (optional, for escalation)

### Installation

1. **Install Dependencies**
```bash
pip install agno openai httpx fastapi uvicorn websockets
```

2. **Set Environment Variables**
```bash
export OPENAI_API_KEY="your-openai-api-key"
export ZENDESK_SUBDOMAIN="your-zendesk-subdomain"  # optional
export ZENDESK_EMAIL="your-zendesk-email"          # optional
export ZENDESK_API_TOKEN="your-zendesk-token"      # optional
```

3. **Update Configuration**
Edit `config.py` to match your setup:
```python
BIGCOMMERCE_MCP_URL = "https://bigcommerce-api-mcp.etugrand.com/mcp"
DATABASE_URL = "postgresql+psycopg://ai:ai@localhost:5532/ai"
KNOWLEDGE_BASE_SOURCES = ["TT/FAQs .md"]
```

### Running the Demo

1. **Automated Demo** (test scenarios):
```bash
python demo.py
```

2. **Interactive Demo** (ask questions directly):
```bash
python demo.py --interactive
```

3. **Web Chat Interface**:
```bash
python web_interface.py
```
Then visit `http://localhost:8001` to see the chat widget.

## 📁 Project Structure

```
teams/bigcommerce_agent/
├── __init__.py                 # Package initialization
├── README.md                   # This file
├── config.py                   # Configuration settings
├── customer_service_team.py    # Main customer service team
├── zendesk_integration.py      # Zendesk escalation tools
├── web_interface.py           # Web chat interface
└── demo.py                    # Demo and testing script
```

## 🔧 Configuration

### Basic Configuration

Update `config.py` with your settings:

```python
class TrendyTransfersConfig:
    # BigCommerce MCP Server
    BIGCOMMERCE_MCP_URL = "https://your-mcp-server.com/mcp"
    
    # Database for knowledge base
    DATABASE_URL = "postgresql+psycopg://user:pass@localhost:5432/db"
    
    # Knowledge base sources (your FAQ files)
    KNOWLEDGE_BASE_SOURCES = [
        "TT/FAQs .md",
        "TT/policies.md",
    ]
    
    # Company information
    COMPANY_NAME = "Trendy Transfers"
    CUSTOMER_SERVICE_EMAIL = "<EMAIL>"
```

### Zendesk Integration (Optional)

For ticket escalation, set these environment variables:
```bash
export ZENDESK_SUBDOMAIN="trendytransfers"
export ZENDESK_EMAIL="<EMAIL>"
export ZENDESK_API_TOKEN="your-zendesk-api-token"
```

## 🎯 Usage Examples

### Programmatic Usage

```python
from customer_service_team import create_customer_service_team

# Initialize the team
team = await create_customer_service_team()

# Handle a customer query
response = await team.handle_query(
    "I need to check my order status",
    customer_email="<EMAIL>"
)

print(response)
```

### Web Integration

The web chat interface creates a small, non-intrusive chat button that can be embedded in your website. It appears in the bottom-right corner and expands when clicked.

To embed in your website:
1. Run the web interface: `python web_interface.py`
2. Include the chat widget in your site by embedding the iframe or copying the HTML/CSS/JS

## 🤖 Agent Capabilities

### Order Management Specialist
- Order status and tracking
- Processing time estimates
- Shipping information
- Order modifications

### Product Information Specialist
- Product details and pricing
- Inventory availability
- Product recommendations
- Customization options

### Customer Account Specialist
- Account access issues
- Profile updates
- Password resets
- Order history

### Billing and Payment Specialist
- Payment questions
- Billing issues
- Refund requests
- Duplicate charges

### Support Escalation Manager
- Creates Zendesk tickets
- Handles complex issues
- Provides ticket tracking
- Manages follow-ups

## 🔌 BigCommerce MCP Integration

The system uses your BigCommerce MCP server to access real-time data:

- **Orders**: `get_all_orders` - Retrieve order information
- **Products**: `get_all_products` - Get product details and inventory
- **Customers**: `get_all_customers` - Access customer information

Make sure your MCP server is running and accessible at the configured URL.

## 📚 Knowledge Base

The system uses a vector database to store and search your FAQ and policy documents. Supported formats:
- Markdown files (`.md`)
- Text files (`.txt`)
- PDF files (`.pdf`)

Add your knowledge sources to `KNOWLEDGE_BASE_SOURCES` in `config.py`.

## 🎫 Zendesk Escalation

When issues require human intervention, the system can automatically:
1. Create Zendesk tickets with full context
2. Set appropriate priority levels
3. Include customer information
4. Provide ticket numbers to customers
5. Tag tickets for proper routing

## 🌐 Web Chat Widget

The web chat interface provides:
- **Non-intrusive design**: Small button in corner
- **Responsive layout**: Works on desktop and mobile
- **Real-time messaging**: WebSocket-based communication
- **Professional styling**: Matches modern web standards
- **Easy embedding**: Can be integrated into any website

## 🔧 Customization

### Adding New Agents

1. Create a new agent in `customer_service_team.py`:
```python
new_agent = Agent(
    name="New Specialist",
    role="Handle specific type of queries",
    model=OpenAIChat(id="gpt-4o"),
    instructions=[...],
)
```

2. Add to the team members list
3. Update routing instructions

### Modifying Instructions

Update agent instructions in `customer_service_team.py` to match your:
- Company policies
- Product information
- Service standards
- Escalation procedures

### Styling the Chat Widget

Modify the CSS in `web_interface.py` to match your website's design:
- Colors and branding
- Fonts and typography
- Layout and positioning
- Animation effects

## 🚨 Troubleshooting

### Common Issues

1. **MCP Connection Failed**
   - Check if your BigCommerce MCP server is running
   - Verify the URL in `config.py`
   - Ensure network connectivity

2. **Knowledge Base Loading Error**
   - Check if knowledge source files exist
   - Verify database connection
   - Ensure proper file permissions

3. **Zendesk Integration Issues**
   - Verify API credentials
   - Check subdomain spelling
   - Ensure API token has proper permissions

### Debug Mode

Enable debug mode in `config.py`:
```python
ENABLE_DEBUG_MODE = True
```

This will show detailed tool calls and agent reasoning.

## 📈 Performance Tips

1. **Database Optimization**: Use a dedicated PostgreSQL instance for production
2. **Caching**: The system caches knowledge base searches
3. **Rate Limiting**: Consider implementing rate limiting for the web interface
4. **Monitoring**: Monitor MCP server response times

## 🤝 Contributing

To contribute to this project:
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is part of the Agno framework ecosystem. Please refer to the main project license.

## 🆘 Support

For support with this customer service agent:
1. Check the troubleshooting section
2. Review the demo script for examples
3. Check Agno documentation
4. Open an issue in the repository

---

**Built with ❤️ using [Agno](https://github.com/agno-agi/agno) - The AI Agent Framework**
