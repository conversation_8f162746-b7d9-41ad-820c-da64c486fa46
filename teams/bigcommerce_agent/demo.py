"""
Demo script for Trendy Transfers BigCommerce Customer Service Agent

This script demonstrates how to use the customer service system with various
types of customer inquiries.
"""

import asyncio
import sys
import os

# Add the parent directory to the path so we can import the modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from customer_service_team import create_customer_service_team
from config import config

async def demo_customer_service():
    """Demonstrate the customer service system with various queries"""
    
    print("🚀 Initializing Trendy Transfers Customer Service Agent...")
    print(f"📡 BigCommerce MCP URL: {config.BIGCOMMERCE_MCP_URL}")
    print(f"📚 Knowledge Sources: {config.KNOWLEDGE_BASE_SOURCES}")
    
    # Validate configuration
    issues = config.validate_config()
    if issues:
        print("❌ Configuration Issues:")
        for issue in issues:
            print(f"   - {issue}")
        print("\nPlease fix these issues before running the demo.")
        return
    
    try:
        # Create the customer service team
        team = await create_customer_service_team(
            bigcommerce_mcp_url=config.BIGCOMMERCE_MCP_URL,
            knowledge_sources=config.KNOWLEDGE_BASE_SOURCES,
            zendesk_config=config.get_zendesk_config()
        )
        
        print("✅ Customer Service Team initialized successfully!")
        print("\n" + "="*60)
        
        # Test queries representing different types of customer inquiries
        test_scenarios = [
            {
                "category": "Order Inquiry",
                "query": "Hi, I placed an order last week but haven't received any tracking information. Can you help me check the status of order #12345?",
                "customer_email": "<EMAIL>"
            },
            {
                "category": "Product Question",
                "query": "Do you have any heat transfer vinyl in metallic gold? What sizes are available and what's the price?",
                "customer_email": None
            },
            {
                "category": "Account Issue",
                "query": "I can't log into my account. I keep getting an error message when I try to reset my password.",
                "customer_email": "<EMAIL>"
            },
            {
                "category": "Billing Question",
                "query": "I was charged twice for my last order. I see two charges on my credit card for the same amount. Can you help me get a refund for the duplicate charge?",
                "customer_email": "<EMAIL>"
            },
            {
                "category": "General FAQ",
                "query": "What's your return policy? How long do I have to return items if they don't work for my project?",
                "customer_email": None
            },
            {
                "category": "Shipping Question",
                "query": "How long does processing usually take? I need my order by Friday for an event.",
                "customer_email": None
            },
            {
                "category": "Complex Issue",
                "query": "I received my order but some of the vinyl is damaged and the colors don't match what I ordered. This is for a wedding this weekend and I'm really stressed. Can someone please help me urgently?",
                "customer_email": "<EMAIL>"
            }
        ]
        
        # Process each test scenario
        for i, scenario in enumerate(test_scenarios, 1):
            print(f"\n🔍 Test Scenario {i}: {scenario['category']}")
            print(f"📧 Customer Email: {scenario['customer_email'] or 'Not provided'}")
            print(f"💬 Query: {scenario['query']}")
            print("\n" + "-"*50)
            
            try:
                # Get response from the customer service team
                response = await team.handle_query(
                    scenario['query'], 
                    scenario['customer_email']
                )
                
                print(f"🤖 Agent Response:")
                print(response)
                
            except Exception as e:
                print(f"❌ Error processing query: {str(e)}")
            
            print("\n" + "="*60)
            
            # Add a small delay between queries
            await asyncio.sleep(1)
        
        print("\n✅ Demo completed successfully!")
        
        # Clean up
        await team.close()
        
    except Exception as e:
        print(f"❌ Error during demo: {str(e)}")
        import traceback
        traceback.print_exc()

async def interactive_demo():
    """Interactive demo where you can ask questions directly"""
    
    print("🚀 Starting Interactive Trendy Transfers Customer Service Demo")
    print("Type 'quit' to exit\n")
    
    try:
        # Initialize the customer service team
        team = await create_customer_service_team(
            bigcommerce_mcp_url=config.BIGCOMMERCE_MCP_URL,
            knowledge_sources=config.KNOWLEDGE_BASE_SOURCES,
            zendesk_config=config.get_zendesk_config()
        )
        
        print("✅ Customer Service Agent is ready!")
        print("💡 Try asking about orders, products, billing, or account issues\n")
        
        while True:
            # Get user input
            user_query = input("👤 Your question: ").strip()
            
            if user_query.lower() in ['quit', 'exit', 'q']:
                break
            
            if not user_query:
                continue
            
            # Optional: ask for customer email
            customer_email = input("📧 Customer email (optional, press Enter to skip): ").strip()
            if not customer_email:
                customer_email = None
            
            print("\n🤖 Agent is thinking...")
            
            try:
                # Get response
                response = await team.handle_query(user_query, customer_email)
                print(f"\n🤖 Agent Response:\n{response}\n")
                
            except Exception as e:
                print(f"\n❌ Error: {str(e)}\n")
        
        print("👋 Thanks for using Trendy Transfers Customer Service!")
        await team.close()
        
    except Exception as e:
        print(f"❌ Error during interactive demo: {str(e)}")

def main():
    """Main function to run the demo"""
    print("Trendy Transfers BigCommerce Customer Service Agent Demo")
    print("="*60)
    
    # Check if we should run interactive mode
    if len(sys.argv) > 1 and sys.argv[1] == "--interactive":
        asyncio.run(interactive_demo())
    else:
        print("Running automated demo scenarios...")
        print("(Use --interactive flag for interactive mode)")
        print()
        asyncio.run(demo_customer_service())

if __name__ == "__main__":
    main()
