name: Build ECR Images

on: workflow_dispatch

permissions:
  # For AWS OIDC Token access as per https://docs.github.com/en/actions/deployment/security-hardening-your-deployments/configuring-openid-connect-in-amazon-web-services#updating-your-github-actions-workflow
  id-token: write # This is required for requesting the JWT
  contents: read # This is required for actions/checkout

env:
  IMAGE_NAME: agent-app
  ECR_REPO: ${{ vars.ECR_REPO }}
  # Create role using https://aws.amazon.com/blogs/security/use-iam-roles-to-connect-github-actions-to-actions-in-aws/
  AWS_ROLE: ${{ vars.AWS_ROLE }}
  AWS_REGION: us-east-1

jobs:
  build-push-image:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ env.AWS_ROLE }}
          aws-region: ${{ env.AWS_REGION }}

      - name: ECR Login
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Build and push image to Amazon ECR
        uses: docker/build-push-action@v5
        with:
          context: .
          file: Dockerfile
          platforms: linux/amd64,linux/arm64
          push: true
          tags: |
            ${{ env.ECR_REPO }}/${{ env.IMAGE_NAME }}:dev
            ${{ env.ECR_REPO }}/${{ env.IMAGE_NAME }}:prd
            ${{ env.ECR_REPO }}/${{ env.IMAGE_NAME }}:${{ github.sha }}
          cache-from: type=gha
          cache-to: type=gha,mode=max