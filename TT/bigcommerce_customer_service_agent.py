"""
BigCommerce Customer Service Agent for Trendy Transfers

This agent provides 24/7 customer support for your BigCommerce store, handling:
- Order inquiries and status updates
- Product information and pricing
- Customer account management
- Billing and payment questions
- General customer service

The agent integrates with:
- BigCommerce MCP Server for real-time data
- Knowledge base for FAQs and policies
- Zendesk for ticket escalation
"""

import asyncio
from typing import Optional
from agno.agent import Agent
from agno.models.openai import OpenAIChat
from agno.team.team import Team
from agno.tools.mcp import MCPTools
from agno.knowledge.text import TextKnowledgeBase
from agno.vectordb.pgvector import PgVector
from agno.app.agui.app import AGUIApp
from zendesk_integration import TrendyTransfersZendesk
from zendesk_integration import TrendyTransfersZendesk

# BigCommerce MCP Server Configuration
BIGCOMMERCE_MCP_URL = "https://bigcommerce-api-mcp.etugrand.com/mcp"

# Knowledge Base Setup (using your FAQ content)
knowledge_base = TextKnowledgeBase(
    sources=[
        "TT/FAQs .md",  # Your existing FAQ file
    ],
    vector_db=PgVector(
        table_name="trendy_transfers_kb",
        db_url="postgresql+psycopg://ai:ai@localhost:5532/ai",  # Update with your DB
    ),
)

class BigCommerceCustomerServiceAgent:
    def __init__(self, zendesk_subdomain: str = None, zendesk_email: str = None, zendesk_api_token: str = None):
        self.zendesk = None
        if zendesk_subdomain and zendesk_email and zendesk_api_token:
            self.zendesk = TrendyTransfersZendesk(zendesk_subdomain, zendesk_email, zendesk_api_token)
        self.setup_agents()
        self.setup_team()
    
    def setup_agents(self):
        """Set up specialized agents for different support functions"""
        
        # Order Management Agent
        self.order_agent = Agent(
            name="Order Management Specialist",
            role="Handle order inquiries, tracking, and status updates",
            model=OpenAIChat(id="gpt-4o"),
            instructions=[
                "You are an order management specialist for Trendy Transfers.",
                "Help customers with order status, tracking, modifications, and general order questions.",
                "Always check the BigCommerce system for real-time order information.",
                "Processing time is 1-3 business days, orders ship from Iowa.",
                "No weekend or holiday processing - Monday through Friday only.",
                "Be empathetic and professional, especially for delayed orders.",
                "If you cannot resolve an issue, escalate to human support.",
                "Always provide order numbers and tracking information when available.",
            ],
            markdown=True,
        )
        
        # Product Information Agent
        self.product_agent = Agent(
            name="Product Information Specialist",
            role="Handle product questions, pricing, and availability",
            model=OpenAIChat(id="gpt-4o"),
            instructions=[
                "You are a product specialist for Trendy Transfers.",
                "Help customers with product information, pricing, availability, and recommendations.",
                "Use the BigCommerce system to get current product details and inventory.",
                "Be knowledgeable about product features, sizing, and customization options.",
                "Suggest related or alternative products when appropriate.",
                "Always provide accurate pricing and availability information.",
            ],
            markdown=True,
        )
        
        # Customer Account Agent
        self.account_agent = Agent(
            name="Customer Account Specialist",
            role="Handle account-related questions and customer information",
            model=OpenAIChat(id="gpt-4o"),
            instructions=[
                "You are a customer account specialist for Trendy Transfers.",
                "Help customers with account access, profile updates, and account-related questions.",
                "Use the BigCommerce customer data to assist with account issues.",
                "For password resets and login issues, guide customers through the process.",
                "If orders are not showing in customer accounts, note this for manual linking.",
                "Protect customer privacy - only discuss account details with verified customers.",
            ],
            markdown=True,
        )
        
        # Billing and Payment Agent
        self.billing_agent = Agent(
            name="Billing and Payment Specialist",
            role="Handle billing questions, payment issues, and refunds",
            model=OpenAIChat(id="gpt-4o"),
            instructions=[
                "You are a billing specialist for Trendy Transfers.",
                "Help customers with payment questions, billing issues, and refund requests.",
                "Be understanding about billing concerns and work to resolve issues quickly.",
                "For refund requests, follow company policy and escalate when necessary.",
                "Explain charges clearly and help customers understand their invoices.",
                "For payment failures, guide customers through retry process.",
            ],
            markdown=True,
        )
        
        # Escalation Manager
        self.escalation_agent = Agent(
            name="Support Escalation Manager",
            role="Handle escalations and create support tickets",
            model=OpenAIChat(id="gpt-4o"),
            instructions=[
                "You are the escalation manager for Trendy Transfers customer support.",
                "When issues cannot be resolved by other agents, create support tickets.",
                "Gather all relevant information before escalating.",
                "Provide customers with ticket numbers and expected response times.",
                "Be professional and reassuring when escalating issues.",
                "Follow up on escalated tickets when possible.",
            ],
            markdown=True,
        )
    
    async def setup_team(self):
        """Set up the customer service team with BigCommerce integration"""
        
        # Initialize BigCommerce MCP Tools
        self.mcp_tools = MCPTools(
            url=BIGCOMMERCE_MCP_URL,
            transport="streamable-http"
        )
        
        # Add MCP tools to relevant agents
        self.order_agent.tools = [self.mcp_tools]
        self.product_agent.tools = [self.mcp_tools]
        self.account_agent.tools = [self.mcp_tools]
        self.billing_agent.tools = [self.mcp_tools]
        
        # Add knowledge base to all agents
        for agent in [self.order_agent, self.product_agent, self.account_agent, self.billing_agent, self.escalation_agent]:
            agent.knowledge = knowledge_base
            agent.search_knowledge = True
        
        # Create the main customer service team
        self.customer_service_team = Team(
            name="Trendy Transfers Customer Service",
            mode="route",
            model=OpenAIChat(id="gpt-4o"),
            enable_team_history=True,
            members=[
                self.order_agent,
                self.product_agent,
                self.account_agent,
                self.billing_agent,
                self.escalation_agent
            ],
            show_tool_calls=True,
            markdown=True,
            debug_mode=False,
            show_members_responses=True,
            instructions=[
                "You are the lead customer service agent for Trendy Transfers.",
                "Analyze customer inquiries and route them to the appropriate specialist:",
                "- Order questions → Order Management Specialist",
                "- Product questions → Product Information Specialist", 
                "- Account issues → Customer Account Specialist",
                "- Billing/payment → Billing and Payment Specialist",
                "- Complex issues → Support Escalation Manager",
                "Always be friendly, professional, and helpful.",
                "Provide clear explanations for routing decisions.",
                "Ensure seamless handoffs between specialists.",
                "Maintain context throughout the conversation.",
                "If unsure about routing, ask clarifying questions.",
            ],
        )
    
    async def initialize(self):
        """Initialize the agent system"""
        await self.mcp_tools.connect()
        # Load knowledge base if not already loaded
        try:
            knowledge_base.load(recreate=False)
        except:
            print("Loading knowledge base for first time...")
            knowledge_base.load(recreate=True)
    
    async def handle_query(self, query: str) -> str:
        """Handle a customer service query"""
        response = await self.customer_service_team.arun(query)
        return response.content
    
    async def close(self):
        """Clean up resources"""
        await self.mcp_tools.close()

# Create the main agent instance
async def create_customer_service_agent():
    """Factory function to create and initialize the customer service agent"""
    agent = BigCommerceCustomerServiceAgent()
    await agent.setup_team()
    await agent.initialize()
    return agent

# Web interface setup
def create_web_app():
    """Create a web interface for the customer service agent"""
    
    # Simple agent for web interface (will be enhanced)
    web_agent = Agent(
        model=OpenAIChat(id="gpt-4o"),
        description="Trendy Transfers Customer Service Agent - helping with orders, products, and account questions",
        instructions=[
            "You are a customer service representative for Trendy Transfers.",
            "Help customers with their questions about orders, products, billing, and account issues.",
            "Be friendly, professional, and helpful at all times.",
            "If you need to access specific order or customer data, let the customer know you're checking the system.",
        ],
        markdown=True,
    )
    
    # Create AGUI app
    agui_app = AGUIApp(
        agent=web_agent,
        name="Trendy Transfers Support",
        app_id="trendy_transfers_support",
        description="24/7 Customer Service for Trendy Transfers - Orders, Products, Billing & More",
    )
    
    return agui_app

if __name__ == "__main__":
    # Example usage
    async def test_agent():
        agent = await create_customer_service_agent()
        
        # Test queries
        test_queries = [
            "I need to check the status of my order #12345",
            "What's your return policy?",
            "I can't log into my account",
            "How much does shipping cost?",
            "I was charged twice for my order",
        ]
        
        for query in test_queries:
            print(f"\n🔍 Query: {query}")
            response = await agent.handle_query(query)
            print(f"💬 Response: {response}")
        
        await agent.close()
    
    # Run test
    asyncio.run(test_agent())
