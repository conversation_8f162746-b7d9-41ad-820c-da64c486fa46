# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This repository contains TrendyTransfers customer support automation assets, including:
- N8N workflow configuration for AI-powered customer support
- FAQ content and knowledge base materials
- Zendesk integration for ticket processing

## Repository Structure

- `workflow.json` - N8N workflow configuration for AI customer support agent
- `readme.md` - System overview and workflow documentation
- `faqs.md` / `faqs.txt` - Customer FAQ content (identical files)
- `links.txt` - Additional reference links (large file, 25k+ tokens)

## N8N Workflow Architecture

The main automation is built in N8N with the following components:

### Core Workflow Nodes
- **Manual Trigger** - Initiates workflow for testing
- **Get Ticket History** - Retrieves Zendesk ticket data (ID: 77719 for testing)
- **AI Support Agent** - LangChain agent with multiple AI tools
- **Format Response** - Structures output for Zendesk
- **Reply to Ticket** - Updates Zendesk ticket with AI response

### AI Agent Tools
1. **Knowledge Base Search** (Pinecone Vector Store)
   - Searches TrendyTransfers vector database
   - Contains FAQs, product info, website content
   - Uses Mistral Cloud embeddings

2. **Fetch Web Content** (HTTP Request Tool)
   - Retrieves live content from URLs
   - Timeout: 30 seconds

3. **Additional Context Search** (Perplexity)
   - Provides broader context beyond knowledge base
   - Uses "sonar" model

### External Services Integration
- **Zendesk API** - Ticket management and responses
- **OpenAI** - GPT-4.1-mini language model
- **Pinecone** - Vector database for knowledge retrieval
- **Mistral Cloud** - Text embeddings
- **Perplexity** - Additional context search

## Business Context

TrendyTransfers specializes in:
- Screen print transfers
- DTF (Direct to Film) transfers
- Vinyl stickers
- Sublimation products
- Heat transfers
- Chenille and leather patches

### Key FAQ Categories
- Order status and tracking
- Shipping policies (free shipping over $75/$150)
- Custom DTF upload process
- Pressing instructions (temperature, time, pressure)
- Pricing and billing
- Account management
- International shipping
- Return policies

### Business Rules
- Business days: Monday-Friday (no weekends/holidays)
- Orders after 11pm CST count as next business day
- Orders ship within 24 hours or less
- Cannot modify orders once placed
- Located in Iowa (sales tax applies to Iowa customers)
- 72-hour window for wrong product complaints

## Development Notes

This is a documentation and configuration repository - no traditional build/test/deployment commands are needed. Changes to the N8N workflow should be made through the N8N interface and exported to `workflow.json`.

The workflow is designed for defensive customer support automation and should not be modified for any potentially malicious purposes.[byterover-mcp]

# important 
always use byterover-retrieve-knowledge tool to get the related context before any tasks 
always use byterover-store-knowledge to store all the critical informations after sucessful tasks