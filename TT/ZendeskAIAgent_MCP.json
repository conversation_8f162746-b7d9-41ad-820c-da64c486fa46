{"nodes": [{"parameters": {"operation": "get", "id": "={{ $json['ticket.id'] }}"}, "name": "Get Tickets", "type": "n8n-nodes-base.zendesk", "position": [-4016, -3680], "typeVersion": 1, "id": "04b23c9a-e791-40c6-aa88-b564a03e898e", "credentials": {"zendeskApi": {"id": "A19TuWN32ugHwWHU", "name": "Zendesk TT"}}}, {"parameters": {"sessionIdType": "customKey", "sessionKey": "={{ $json.id }}", "contextWindowLength": 20}, "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1.3, "position": [-2064, -3280], "id": "631f11dd-1db0-40f1-8de2-80514688904e", "name": "Conversation Memory"}, {"parameters": {"model": {"__rl": true, "value": "gpt-5-mini", "mode": "list", "cachedResultName": "gpt-5-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [-2240, -3264], "id": "bd4589c2-cce8-4f63-895e-371ded268539", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "M4FlKeBNFXuCLdGm", "name": "OpenAi account"}}}, {"parameters": {"mode": "retrieve-as-tool", "toolDescription": "Search the knowledge base for FAQs, product information, and website content related to customer inquiries. Use this tool to find relevant documentation and support materials.", "pineconeIndex": {"__rl": true, "value": "trendytransfers", "mode": "list", "cachedResultName": "trendytransfers"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.vectorStorePinecone", "typeVersion": 1.3, "position": [-2048, -3120], "id": "461cb5bd-1db8-44f8-b73f-8b06344416ab", "name": "Knowledge Base Search", "credentials": {"pineconeApi": {"id": "L0nFOJ4KXFcGKXlp", "name": "PineconeApi account"}}}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.embeddingsMistralCloud", "typeVersion": 1, "position": [-1968, -3008], "id": "ad87aa7f-7a59-4d0f-8fe0-fbce78ed8d0a", "name": "Embeddings Model", "credentials": {"mistralCloudApi": {"id": "fCxjRSUWmtgznJp1", "name": "Mistral Cloud account"}}}, {"parameters": {"model": "sonar", "messages": {"message": [{"content": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('message0_Text', ``, 'string') }}"}]}, "simplify": true, "options": {}, "requestOptions": {}}, "type": "n8n-nodes-base.perplexityTool", "typeVersion": 1, "position": [-1696, -3264], "id": "d15dc7ef-57b5-4f69-a5bc-14bece1d97ed", "name": "Additional Context Search", "credentials": {"perplexityApi": {"id": "VSScJSR7mIyGdLnB", "name": "Perplexity account"}}}, {"parameters": {"promptType": "define", "text": "=You are a helpful customer support AI agent for TrendyTransfers. Your role is to provide accurate, helpful, and professional responses to customer inquiries.\n\n**Customer Ticket:**\nSubject: {{ $json.ticket_subject || null }}\nDescription: {{ $json.ticket_description || null }}\n\nCustomer_Profile: {{ $json[' ai_customer_profile'] || null }}\n\nProducts: {{ $json[' customer_products'] || null }}\n\nProduct_context: {{ $json[' ai_product_context'] || null }}\n\nAttachment_url: {{ $json['attachment_url'] || null }}", "options": {"systemMessage": "=You are **TrendyTransfers' AI customer support agent** with access to the company's knowledge base, website content, BigCommerce customer data, and external sources. Always respond professionally, accurately, and helpfully. Use available tools as needed, but do not explain your internal process or tool usage to the customer — only share what is relevant to their request.\n\n## Tools\n\n1. **Pinecone Vector Store** – Primary source for FAQs, product info, policies, and support docs.\n2. **Perplexity Search** – Supplement knowledge base results with broader technical or industry context.\n3. **Simple Memory** – Maintains ticket-specific conversation history for continuity.\n4. **AttachmentContent** – Extracts content from images the user mentions.\n5. **BigCommerce Customer Data** – Access customer orders, products, and account information via MCP tools.\n\n## Response Protocol\n\n1. Start with **Pinecone Vector Store**.\n2. Use **BigCommerce MCP tools** for order status, customer history, and product details.\n3. Use **Perplexity** if extra context is needed.\n4. Keep tone natural, concise, and human-like.\n5. Provide only the necessary, actionable details.\n6. Offer next steps or assistance without overexplaining.\n\n## Style Rules\n\n* Do **not** use meta phrases like \"short answer,\" \"long story short,\" \"basically,\" or comments on the structure of your reply.\n* Avoid preambles and filler. Start directly with helpful information.\n* Be conversational yet professional — like a skilled human support rep.\n* Use bullet points or numbered lists only when they make the information clearer.\n* Reference relevant details or links without mentioning which tools were used.\n* If information is incomplete, acknowledge briefly and suggest next steps.\n* Sign every response exactly as follows:\n\nBest regards,  \nIsaac  \nCustomer Service Team | Don't forget to download the Trendy Transfers App | https://bit.ly/4h7VpI0 - iOS  \nhttps://bit.ly/3UbaFtI - Android  \n\nVisit us on Faire: https://trendytransfers.faire.com\n\n\n## Tool Strategy\n\n* Start with **Pinecone** → use **BigCommerce MCP** for customer data → use **Perplexity** for extra context.\n* Use tools internally; never mention them to the customer.\n"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2, "position": [-2112, -3520], "id": "1cf4f406-3d2b-4703-8cd7-06b3fbd4dd80", "name": "AI Support Agent"}, {"parameters": {"promptType": "define", "text": "=You are a Customer Data Specialist AI for TrendyTransfers customer support. Your role is to retrieve and analyze customer information from BigCommerce.\n\n**Task:** Find customer information for: {{ $json.customer_email }}\n\n**Instructions:**\n1. Use the BigCommerce MCP tools to search for customer data by email\n2. If customer exists, gather comprehensive customer profile including:\n   - Customer ID, name, email, phone\n   - Account creation date\n   - Customer group/status\n   - Address information\n   - Any customer notes or tags\n3. If customer not found, return clear \"not found\" status\n4. Format the response for the support workflow\n\n**Return format:**\n- customer_found: true/false\n- customer_data: {customer object if found}\n- search_email: {email searched}\n- error_message: {if any errors occurred}", "options": {"systemMessage": "You are a specialized AI agent for retrieving BigCommerce customer data. Use the available MCP tools to search for customer information efficiently. Always return structured data that can be easily processed by the next workflow step."}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2, "position": [-3264, -3680], "id": "customer-data-agent", "name": "Customer Data Agent"}, {"parameters": {"promptType": "define", "text": "=You are an Order Data Specialist AI for TrendyTransfers customer support. Your role is to retrieve and analyze customer order history from BigCommerce.\n\n**Task:** Find order history for customer ID: {{ $json.customer_id }}\n\n**Instructions:**\n1. Use the BigCommerce MCP tools to get all orders for this customer\n2. Sort orders by date (most recent first)\n3. For each order, extract key information:\n   - Order ID, status, date\n   - Total amount, payment status\n   - Shipping status and tracking info\n   - Items ordered\n4. Identify any pending, shipped, or problematic orders\n5. Calculate customer lifetime value and order patterns\n\n**Return format:**\n- orders_found: true/false\n- order_count: {number of orders}\n- orders: [{array of formatted order objects}]\n- customer_summary: {spending patterns, order frequency, etc.}\n- recent_activity: {last 30 days activity}\n- issues_detected: {any orders needing attention}", "options": {"systemMessage": "You are a specialized AI agent for retrieving BigCommerce order data. Focus on gathering comprehensive order information that will help customer support agents provide accurate, helpful responses about order status, shipping, and customer history."}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2, "position": [-2896, -3776], "id": "order-data-agent", "name": "Order Data Agent"}, {"parameters": {"promptType": "define", "text": "=You are a Product Data Specialist AI for TrendyTransfers customer support. Your role is to retrieve detailed product information for customer orders.\n\n**Task:** Get product details for orders: {{ $json.order_ids }}\n\n**Instructions:**\n1. Use the BigCommerce MCP tools to get product information for items in customer orders\n2. For each product, gather:\n   - Product name, SKU, description\n   - Current availability/stock status\n   - Product images and specifications\n   - Categories and tags\n   - Pricing information\n   - Any product variations (size, color, etc.)\n3. Check if any products have been discontinued or updated\n4. Identify products that commonly have support questions\n\n**Return format:**\n- products: [{array of detailed product objects}]\n- discontinued_products: [{any products no longer available}]\n- stock_issues: [{products with low/no stock}]\n- product_context: {summary of product types and any relevant support info}", "options": {"systemMessage": "You are a specialized AI agent for retrieving BigCommerce product data. Focus on gathering product information that will help customer support agents answer questions about items customers have ordered, including availability, specifications, and common support issues."}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2, "position": [-2576, -3776], "id": "product-data-agent", "name": "Product Data Agent"}, {"parameters": {"model": "gpt-4o-mini", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [-3296, -3584], "id": "customer-data-model", "name": "Customer Data Model", "credentials": {"openAiApi": {"id": "M4FlKeBNFXuCLdGm", "name": "OpenAi account"}}}, {"parameters": {"model": "gpt-4o-mini", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [-2928, -3680], "id": "order-data-model", "name": "Order Data Model", "credentials": {"openAiApi": {"id": "M4FlKeBNFXuCLdGm", "name": "OpenAi account"}}}, {"parameters": {"model": "gpt-4o-mini", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [-2608, -3680], "id": "product-data-model", "name": "Product Data Model", "credentials": {"openAiApi": {"id": "M4FlKeBNFXuCLdGm", "name": "OpenAi account"}}}, {"parameters": {"endpointUrl": "https://bigcommerce-api-mcp.etugrand.com/mcp", "serverTransport": "httpStreamable", "authentication": "headerAuth", "include": "selected", "includeTools": ["get_all_customers"]}, "type": "@n8n/n8n-nodes-langchain.mcpClientTool", "typeVersion": 1.1, "position": [-3168, -3584], "id": "mcp-customers-tool", "name": "MCP Customers Tool", "credentials": {"httpHeaderAuth": {"id": "ifTkdbjRiuwZxdxe", "name": "TT Bigcommerce MCP"}}}, {"parameters": {"endpointUrl": "https://bigcommerce-api-mcp.etugrand.com/mcp", "serverTransport": "httpStreamable", "authentication": "headerAuth", "include": "selected", "includeTools": ["get_all_orders"]}, "type": "@n8n/n8n-nodes-langchain.mcpClientTool", "typeVersion": 1.1, "position": [-2800, -3680], "id": "mcp-orders-tool", "name": "MCP Orders Tool", "credentials": {"httpHeaderAuth": {"id": "ifTkdbjRiuwZxdxe", "name": "TT Bigcommerce MCP"}}}, {"parameters": {"endpointUrl": "https://bigcommerce-api-mcp.etugrand.com/mcp", "serverTransport": "httpStreamable", "authentication": "headerAuth", "include": "selected", "includeTools": ["get_all_products"]}, "type": "@n8n/n8n-nodes-langchain.mcpClientTool", "typeVersion": 1.1, "position": [-2480, -3680], "id": "mcp-products-tool", "name": "MCP Products Tool", "credentials": {"httpHeaderAuth": {"id": "ifTkdbjRiuwZxdxe", "name": "TT Bigcommerce MCP"}}}, {"parameters": {"jsCode": "// Customer Context Processor - Combines all BigCommerce data for AI agent\n// Input: Customer data, order data, product data from MCP agents\n// Output: Formatted context for main AI support agent\n\nconst allInputs = $input.all();\n\n// Initialize context object\nconst customerContext = {\n  customer_profile: null,\n  order_summary: null,\n  product_details: null,\n  ai_prompt_context: '',\n  processing_errors: []\n};\n\n// Process each input\nallInputs.forEach((input, index) => {\n  const data = input.json;\n  \n  // Identify input source by checking data structure\n  if (data.customer_found !== undefined) {\n    // Customer data from Customer Data Agent\n    customerContext.customer_profile = data;\n  } else if (data.orders_found !== undefined) {\n    // Order data from Order Data Agent\n    customerContext.order_summary = data;\n  } else if (data.products !== undefined) {\n    // Product data from Product Data Agent\n    customerContext.product_details = data;\n  } else {\n    customerContext.processing_errors.push(`Unknown input format at index ${index}`);\n  }\n});\n\n// Generate comprehensive AI prompt context\nfunction generateAIContext() {\n  let context = '';\n  \n  // Customer Profile Section\n  if (customerContext.customer_profile?.customer_found) {\n    const customer = customerContext.customer_profile.customer_data;\n    context += `\\n**CUSTOMER PROFILE:**\\n`;\n    context += `- Name: ${customer.first_name} ${customer.last_name}\\n`;\n    context += `- Email: ${customer.email}\\n`;\n    context += `- Phone: ${customer.phone || 'Not provided'}\\n`;\n    context += `- Customer ID: ${customer.id}\\n`;\n    context += `- Account Created: ${new Date(customer.date_created).toLocaleDateString()}\\n`;\n    context += `- Customer Group: ${customer.customer_group_name || 'Default'}\\n`;\n  } else {\n    context += `\\n**CUSTOMER PROFILE:**\\n- Customer not found in BigCommerce\\n- Email searched: ${customerContext.customer_profile?.search_email || 'Unknown'}\\n`;\n  }\n  \n  // Order History Section\n  if (customerContext.order_summary?.orders_found) {\n    const orders = customerContext.order_summary;\n    context += `\\n**ORDER HISTORY:**\\n`;\n    context += `- Total Orders: ${orders.order_count}\\n`;\n    context += `- Customer Lifetime Value: $${orders.customer_summary?.lifetime_value || 0}\\n`;\n    context += `- Average Order Value: $${orders.customer_summary?.avg_order_value || 0}\\n`;\n    \n    if (orders.recent_activity?.has_recent_orders) {\n      context += `- Recent Activity: Yes (last 30 days)\\n`;\n    }\n    \n    // Recent orders (last 3)\n    if (orders.orders && orders.orders.length > 0) {\n      context += `\\n**RECENT ORDERS:**\\n`;\n      orders.orders.slice(0, 3).forEach(order => {\n        context += `- Order #${order.id}: ${order.status} - $${order.total_inc_tax} (${new Date(order.date_created).toLocaleDateString()})\\n`;\n      });\n    }\n    \n    // Issues requiring attention\n    if (orders.issues_detected && orders.issues_detected.length > 0) {\n      context += `\\n**ISSUES REQUIRING ATTENTION:**\\n`;\n      orders.issues_detected.forEach(issue => {\n        context += `- ${issue}\\n`;\n      });\n    }\n  } else {\n    context += `\\n**ORDER HISTORY:**\\n- No orders found for this customer\\n`;\n  }\n  \n  // Product Details Section\n  if (customerContext.product_details?.products?.length > 0) {\n    const products = customerContext.product_details;\n    context += `\\n**CUSTOMER'S PRODUCTS:**\\n`;\n    products.products.forEach(product => {\n      context += `- ${product.name} (SKU: ${product.sku})\\n`;\n    });\n    \n    if (products.stock_issues?.length > 0) {\n      context += `\\n**STOCK ALERTS:**\\n`;\n      products.stock_issues.forEach(issue => {\n        context += `- ${issue}\\n`;\n      });\n    }\n  }\n  \n  // Processing errors\n  if (customerContext.processing_errors.length > 0) {\n    context += `\\n**PROCESSING NOTES:**\\n`;\n    customerContext.processing_errors.forEach(error => {\n      context += `- ${error}\\n`;\n    });\n  }\n  \n  return context;\n}\n\n// Generate final context\ncustomerContext.ai_prompt_context = generateAIContext();\n\n// Return formatted data for main AI agent\nreturn [{\n  json: {\n    ai_customer_profile: customerContext.ai_prompt_context,\n    customer_found: customerContext.customer_profile?.customer_found || false,\n    has_orders: customerContext.order_summary?.orders_found || false,\n    order_count: customerContext.order_summary?.order_count || 0,\n    product_count: customerContext.product_details?.products?.length || 0,\n    processing_timestamp: new Date().toISOString(),\n    raw_data: {\n      customer: customerContext.customer_profile,\n      orders: customerContext.order_summary,\n      products: customerContext.product_details\n    }\n  }\n}];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-2416, -3520], "id": "customer-context-processor", "name": "Customer Context Processor"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "check-customer-exists", "leftValue": "={{ $json.customer_found }}", "rightValue": true, "operator": {"type": "boolean", "operation": "equal"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-3104, -3680], "id": "customer-exists-check", "name": "Customer Exists Check"}, {"parameters": {"tableId": "support_tickets", "fieldsUi": {"fieldValues": [{"fieldId": "ticket_id", "fieldValue": "={{ $('Get Tickets').item.json.id }}"}, {"fieldId": "name", "fieldValue": "={{ $json.customer_name }}"}, {"fieldId": "email", "fieldValue": "={{ $json.customer_email }}"}, {"fieldId": "subject", "fieldValue": "={{ $('Get Tickets').item.json.subject }}"}, {"fieldId": "description", "fieldValue": "={{ $('Get Tickets').item.json.description }}"}, {"fieldId": "attachment_url", "fieldValue": "={{ $json.attachment_url }}"}, {"fieldId": "data_status", "fieldValue": "open"}]}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-3456, -3680], "id": "1c0eb8a5-3246-4fe0-9826-4693934c0c5c", "name": "Create a row", "credentials": {"supabaseApi": {"id": "OIj2n9BWAeftzvdz", "name": "Supabase account"}}}, {"parameters": {"assignments": {"assignments": [{"id": "ticket-subject", "name": "ticket_subject", "value": "={{ $json.subject }}", "type": "string"}, {"id": "ticket-description", "name": "ticket_description", "value": "={{ $json.description }}", "type": "string"}, {"id": "customer-profile", "name": " ai_customer_profile", "value": "={{ $json.ai_customer_profile }}", "type": "string"}, {"id": "attachment-url", "name": "attachment_url", "value": "={{ $json.attachment_url }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-2272, -3520], "id": "ticket-details-set", "name": "Set Ticket Details"}, {"parameters": {"operation": "update", "id": "={{ $('Create a row').item.json.ticket_id }}", "updateFields": {"publicReply": "={{ $json.response }}", "status": "pending"}}, "type": "n8n-nodes-base.zendesk", "typeVersion": 1, "position": [-1536, -3520], "id": "28e82ecf-4be6-41e4-b09f-8d7dede0835d", "name": "Update a ticket", "credentials": {"zendeskApi": {"id": "A19TuWN32ugHwWHU", "name": "Zendesk TT"}}}, {"parameters": {"operation": "get", "tableId": "support_tickets", "filters": {"conditions": [{"keyName": "ticket_id", "keyValue": "={{ $('Zendesk Trigger').item.json['ticket.id'] }}"}, {"keyName": "data_status", "keyValue": "open"}]}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-2848, -3536], "id": "46e4311d-d5e7-4e61-b1b6-a1d599d11adb", "name": "Get a row", "credentials": {"supabaseApi": {"id": "OIj2n9BWAeftzvdz", "name": "Supabase account"}}}, {"parameters": {"options": {"fields": ["ticket.title", "ticket.description", "ticket.url", "ticket.id", "ticket.status", "ticket.ticket_field_10665924517143"]}, "conditions": {"any": [{"value": "new"}]}}, "type": "n8n-nodes-base.zendeskTrigger", "typeVersion": 1, "position": [-4176, -3680], "id": "7bd0ceaa-7b70-46a0-8f44-53c317c4f6f4", "name": "Zendesk Trigger", "webhookId": "2b44cf6c-55cc-4ebc-9292-b1bc65935a8d", "credentials": {"zendeskApi": {"id": "A19TuWN32ugHwWHU", "name": "Zendesk TT"}}}, {"parameters": {"url": "=https://trendytransfers.zendesk.com/api/v2/tickets/{{ $json.id }}/comments.json", "authentication": "predefinedCredentialType", "nodeCredentialType": "zendeskApi", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-3808, -3680], "id": "********-4c5c-4e0a-bfd5-085392d81ce3", "name": "Comments", "credentials": {"httpHeaderAuth": {"id": "jlJIt9QpzBuOWgwC", "name": "Trendy Transferts BigCommerce"}, "zendeskApi": {"id": "A19TuWN32ugHwWHU", "name": "Zendesk TT"}}}, {"parameters": {"assignments": {"assignments": [{"id": "customer-email", "name": "customer_email", "value": "={{ $json.comments[0].via.source.from.address }}", "type": "string"}, {"id": "customer-name", "name": "customer_name", "value": "={{ $json.comments[0].via.source.from.name }}", "type": "string"}, {"id": "ticket-description", "name": "ticket_description", "value": "={{ $json.comments[0].body }}", "type": "string"}, {"id": "attachment-url", "name": "attachment_url", "value": "={{ $json.comments[0].attachments[0].content_url }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-3648, -3680], "id": "99fd4162-c28e-4f7f-8a6f-0146e646ab0b", "name": "Customer Details"}, {"parameters": {"description": "Call this tool to get the content of an attachment", "workflowId": {"__rl": true, "value": "T53WDlvRuYwfIFPT", "mode": "list", "cachedResultName": "Zendesk_OCR_Tool"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {"attachment_url": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('attachment_url', ``, 'string') }}"}, "matchingColumns": ["attachment_url"], "schema": [{"id": "attachment_url", "displayName": "attachment_url", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string", "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "typeVersion": 2.2, "position": [-1904, -3264], "id": "de628e50-24a5-422b-8e88-31efeef79e51", "name": "AttachmentC<PERSON>nt"}, {"parameters": {"operation": "update", "tableId": "support_tickets", "filters": {"conditions": [{"keyName": "id", "condition": "eq", "keyValue": "={{ $('Create a row').first().json.id }}"}]}, "fieldsUi": {"fieldValues": [{"fieldId": "response", "fieldValue": "={{ $json.output }}"}, {"fieldId": "data_status", "fieldValue": "responded"}]}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-1792, -3520], "id": "491d1095-b757-4bce-a519-23c44237c380", "name": "Update response", "credentials": {"supabaseApi": {"id": "OIj2n9BWAeftzvdz", "name": "Supabase account"}}}], "connections": {"Get Tickets": {"main": [[{"node": "Comments", "type": "main", "index": 0}]]}, "Conversation Memory": {"ai_memory": [[{"node": "AI Support Agent", "type": "ai_memory", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Support Agent", "type": "ai_languageModel", "index": 0}]]}, "Knowledge Base Search": {"ai_tool": [[{"node": "AI Support Agent", "type": "ai_tool", "index": 0}]]}, "Embeddings Model": {"ai_embedding": [[{"node": "Knowledge Base Search", "type": "ai_embedding", "index": 0}]]}, "Additional Context Search": {"ai_tool": [[{"node": "AI Support Agent", "type": "ai_tool", "index": 0}]]}, "AI Support Agent": {"main": [[{"node": "Update response", "type": "main", "index": 0}]]}, "Customer Data Agent": {"main": [[{"node": "Customer Exists Check", "type": "main", "index": 0}]]}, "Order Data Agent": {"main": [[{"node": "Customer Context Processor", "type": "main", "index": 0}]]}, "Product Data Agent": {"main": [[{"node": "Customer Context Processor", "type": "main", "index": 0}]]}, "Customer Data Model": {"ai_languageModel": [[{"node": "Customer Data Agent", "type": "ai_languageModel", "index": 0}]]}, "Order Data Model": {"ai_languageModel": [[{"node": "Order Data Agent", "type": "ai_languageModel", "index": 0}]]}, "Product Data Model": {"ai_languageModel": [[{"node": "Product Data Agent", "type": "ai_languageModel", "index": 0}]]}, "MCP Customers Tool": {"ai_tool": [[{"node": "Customer Data Agent", "type": "ai_tool", "index": 0}]]}, "MCP Orders Tool": {"ai_tool": [[{"node": "Order Data Agent", "type": "ai_tool", "index": 0}]]}, "MCP Products Tool": {"ai_tool": [[{"node": "Product Data Agent", "type": "ai_tool", "index": 0}]]}, "Customer Context Processor": {"main": [[{"node": "Set Ticket Details", "type": "main", "index": 0}]]}, "Customer Exists Check": {"main": [[{"node": "Order Data Agent", "type": "main", "index": 0}, {"node": "Customer Context Processor", "type": "main", "index": 0}], [{"node": "Get a row", "type": "main", "index": 0}]]}, "Create a row": {"main": [[{"node": "Customer Data Agent", "type": "main", "index": 0}]]}, "Set Ticket Details": {"main": [[{"node": "AI Support Agent", "type": "main", "index": 0}]]}, "Update a ticket": {"main": []}, "Get a row": {"main": [[{"node": "Set Ticket Details", "type": "main", "index": 0}]]}, "Zendesk Trigger": {"main": [[{"node": "Get Tickets", "type": "main", "index": 0}]]}, "Comments": {"main": [[{"node": "Customer Details", "type": "main", "index": 0}]]}, "Customer Details": {"main": [[{"node": "Create a row", "type": "main", "index": 0}]]}, "AttachmentContent": {"ai_tool": [[{"node": "AI Support Agent", "type": "ai_tool", "index": 0}]]}, "Update response": {"main": [[{"node": "Update a ticket", "type": "main", "index": 0}]]}}, "pinData": {}, "meta": {"templateCredsSetupCompleted": true, "instanceId": "a328d5870890b1de9c157562c3d6adbde0f1891acff46b62a31fafe6d04b3436"}}