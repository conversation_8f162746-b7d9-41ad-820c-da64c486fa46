"""
Zendesk Integration for Trendy Transfers Customer Service

This module provides tools for creating and managing Zendesk tickets
when customer issues need to be escalated to human support.
"""

import httpx
import json
from typing import Optional, Dict, Any
from agno.tools.base import Toolkit
from pydantic import BaseModel, Field

class ZendeskTicket(BaseModel):
    """Model for Zendesk ticket creation"""
    subject: str = Field(description="Ticket subject")
    description: str = Field(description="Detailed description of the issue")
    priority: str = Field(default="normal", description="Ticket priority: low, normal, high, urgent")
    type: str = Field(default="question", description="Ticket type: question, incident, problem, task")
    customer_email: Optional[str] = Field(default=None, description="Customer email address")
    customer_name: Optional[str] = Field(default=None, description="Customer name")
    tags: list[str] = Field(default_factory=list, description="Tags for categorization")

class ZendeskTools(Toolkit):
    """Zendesk integration tools for customer service escalation"""
    
    def __init__(
        self,
        subdomain: str,
        email: str,
        api_token: str,
        **kwargs
    ):
        super().__init__(**kwargs)
        self.subdomain = subdomain
        self.email = email
        self.api_token = api_token
        self.base_url = f"https://{subdomain}.zendesk.com/api/v2"
        
        # Setup HTTP client with authentication
        self.client = httpx.AsyncClient(
            auth=(f"{email}/token", api_token),
            headers={"Content-Type": "application/json"},
            timeout=30.0
        )
    
    async def create_ticket(
        self,
        subject: str,
        description: str,
        customer_email: Optional[str] = None,
        customer_name: Optional[str] = None,
        priority: str = "normal",
        ticket_type: str = "question",
        tags: Optional[list[str]] = None
    ) -> Dict[str, Any]:
        """
        Create a new Zendesk ticket for customer support escalation.
        
        Args:
            subject: Brief description of the issue
            description: Detailed description of the customer's problem
            customer_email: Customer's email address
            customer_name: Customer's name
            priority: Ticket priority (low, normal, high, urgent)
            ticket_type: Type of ticket (question, incident, problem, task)
            tags: List of tags for categorization
        
        Returns:
            Dictionary containing ticket information including ticket ID and URL
        """
        try:
            # Prepare ticket data
            ticket_data = {
                "ticket": {
                    "subject": subject,
                    "comment": {"body": description},
                    "priority": priority,
                    "type": ticket_type,
                    "tags": tags or ["ai-escalation", "customer-service"]
                }
            }
            
            # Add requester information if provided
            if customer_email:
                ticket_data["ticket"]["requester"] = {"email": customer_email}
                if customer_name:
                    ticket_data["ticket"]["requester"]["name"] = customer_name
            
            # Create the ticket
            response = await self.client.post(
                f"{self.base_url}/tickets.json",
                json=ticket_data
            )
            response.raise_for_status()
            
            result = response.json()
            ticket = result["ticket"]
            
            return {
                "success": True,
                "ticket_id": ticket["id"],
                "ticket_url": ticket["url"],
                "status": ticket["status"],
                "created_at": ticket["created_at"],
                "message": f"Ticket #{ticket['id']} created successfully"
            }
            
        except httpx.HTTPStatusError as e:
            return {
                "success": False,
                "error": f"HTTP error {e.response.status_code}: {e.response.text}",
                "message": "Failed to create ticket due to API error"
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": "Failed to create ticket due to unexpected error"
            }
    
    async def get_ticket(self, ticket_id: int) -> Dict[str, Any]:
        """
        Retrieve information about a specific ticket.
        
        Args:
            ticket_id: The Zendesk ticket ID
        
        Returns:
            Dictionary containing ticket information
        """
        try:
            response = await self.client.get(f"{self.base_url}/tickets/{ticket_id}.json")
            response.raise_for_status()
            
            result = response.json()
            ticket = result["ticket"]
            
            return {
                "success": True,
                "ticket_id": ticket["id"],
                "subject": ticket["subject"],
                "status": ticket["status"],
                "priority": ticket["priority"],
                "created_at": ticket["created_at"],
                "updated_at": ticket["updated_at"],
                "description": ticket.get("description", ""),
                "tags": ticket.get("tags", [])
            }
            
        except httpx.HTTPStatusError as e:
            return {
                "success": False,
                "error": f"HTTP error {e.response.status_code}: {e.response.text}",
                "message": f"Failed to retrieve ticket #{ticket_id}"
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": f"Failed to retrieve ticket #{ticket_id} due to unexpected error"
            }
    
    async def add_comment(
        self,
        ticket_id: int,
        comment: str,
        public: bool = True
    ) -> Dict[str, Any]:
        """
        Add a comment to an existing ticket.
        
        Args:
            ticket_id: The Zendesk ticket ID
            comment: The comment text to add
            public: Whether the comment is public (visible to customer) or private
        
        Returns:
            Dictionary containing the result of the operation
        """
        try:
            comment_data = {
                "ticket": {
                    "comment": {
                        "body": comment,
                        "public": public
                    }
                }
            }
            
            response = await self.client.put(
                f"{self.base_url}/tickets/{ticket_id}.json",
                json=comment_data
            )
            response.raise_for_status()
            
            return {
                "success": True,
                "message": f"Comment added to ticket #{ticket_id}",
                "ticket_id": ticket_id
            }
            
        except httpx.HTTPStatusError as e:
            return {
                "success": False,
                "error": f"HTTP error {e.response.status_code}: {e.response.text}",
                "message": f"Failed to add comment to ticket #{ticket_id}"
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": f"Failed to add comment to ticket #{ticket_id} due to unexpected error"
            }
    
    async def search_tickets(
        self,
        query: str,
        limit: int = 10
    ) -> Dict[str, Any]:
        """
        Search for tickets based on a query.
        
        Args:
            query: Search query (can include customer email, subject keywords, etc.)
            limit: Maximum number of results to return
        
        Returns:
            Dictionary containing search results
        """
        try:
            params = {
                "query": query,
                "sort_by": "updated_at",
                "sort_order": "desc"
            }
            
            response = await self.client.get(
                f"{self.base_url}/search.json",
                params=params
            )
            response.raise_for_status()
            
            result = response.json()
            tickets = result.get("results", [])[:limit]
            
            return {
                "success": True,
                "count": len(tickets),
                "tickets": [
                    {
                        "id": ticket["id"],
                        "subject": ticket["subject"],
                        "status": ticket["status"],
                        "priority": ticket["priority"],
                        "created_at": ticket["created_at"],
                        "updated_at": ticket["updated_at"]
                    }
                    for ticket in tickets
                ]
            }
            
        except httpx.HTTPStatusError as e:
            return {
                "success": False,
                "error": f"HTTP error {e.response.status_code}: {e.response.text}",
                "message": "Failed to search tickets"
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": "Failed to search tickets due to unexpected error"
            }
    
    async def close(self):
        """Close the HTTP client"""
        await self.client.aclose()

# Example usage and configuration
class TrendyTransfersZendesk:
    """Trendy Transfers specific Zendesk configuration"""
    
    def __init__(self, subdomain: str, email: str, api_token: str):
        self.zendesk = ZendeskTools(
            subdomain=subdomain,
            email=email,
            api_token=api_token
        )
    
    async def escalate_customer_issue(
        self,
        customer_query: str,
        customer_email: Optional[str] = None,
        customer_name: Optional[str] = None,
        issue_category: str = "general",
        ai_analysis: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Escalate a customer issue to Zendesk with proper categorization.
        
        Args:
            customer_query: The original customer question/issue
            customer_email: Customer's email address
            customer_name: Customer's name
            issue_category: Category of the issue (order, billing, product, account, etc.)
            ai_analysis: AI agent's analysis of the issue
        
        Returns:
            Dictionary containing escalation result
        """
        
        # Determine priority based on keywords
        priority = "normal"
        urgent_keywords = ["urgent", "emergency", "asap", "immediately", "critical"]
        high_keywords = ["refund", "charged", "billing", "payment", "error", "broken"]
        
        query_lower = customer_query.lower()
        if any(keyword in query_lower for keyword in urgent_keywords):
            priority = "urgent"
        elif any(keyword in query_lower for keyword in high_keywords):
            priority = "high"
        
        # Create subject
        subject = f"[AI Escalation] {issue_category.title()} Issue"
        if customer_name:
            subject += f" - {customer_name}"
        
        # Create detailed description
        description = f"""
Customer Issue Escalated from AI Support:

CUSTOMER QUERY:
{customer_query}

ISSUE CATEGORY: {issue_category.title()}

CUSTOMER INFORMATION:
- Name: {customer_name or 'Not provided'}
- Email: {customer_email or 'Not provided'}

AI ANALYSIS:
{ai_analysis or 'No additional analysis provided'}

ESCALATION REASON:
This issue requires human intervention as it could not be resolved through automated support.

Please follow up with the customer promptly.
        """.strip()
        
        # Determine tags
        tags = ["ai-escalation", "customer-service", issue_category]
        
        # Create the ticket
        result = await self.zendesk.create_ticket(
            subject=subject,
            description=description,
            customer_email=customer_email,
            customer_name=customer_name,
            priority=priority,
            ticket_type="question",
            tags=tags
        )
        
        return result
    
    async def close(self):
        """Close the Zendesk connection"""
        await self.zendesk.close()

# Configuration helper
def create_zendesk_integration(subdomain: str, email: str, api_token: str) -> TrendyTransfersZendesk:
    """
    Create a Zendesk integration instance for Trendy Transfers.
    
    Args:
        subdomain: Your Zendesk subdomain (e.g., 'trendytransfers')
        email: Your Zendesk admin email
        api_token: Your Zendesk API token
    
    Returns:
        TrendyTransfersZendesk instance
    """
    return TrendyTransfersZendesk(subdomain, email, api_token)
