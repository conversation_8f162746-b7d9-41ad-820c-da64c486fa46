from os import getenv

from agno.playground import Playground

from agents.sage import get_sage
from agents.scholar import get_scholar
from agents.agno_support import get_agno_support_agent
from teams.finance_researcher import get_finance_researcher_team
from teams.multi_language import get_multi_language_team

######################################################
## Router for the Playground Interface
######################################################

sage_agent = get_sage(debug_mode=True)
scholar_agent = get_scholar(debug_mode=True)
agno_support_agent = get_agno_support_agent(debug_mode=True)
finance_researcher_team = get_finance_researcher_team(debug_mode=True)
multi_language_team = get_multi_language_team(debug_mode=True)

# Create a playground instance
playground = Playground(agents=[sage_agent, scholar_agent, agno_support_agent], teams=[finance_researcher_team, multi_language_team])

# Register the endpoint where playground routes are served with agno.com
# Note: In Docker/production, we don't start a separate playground server
# The playground routes are served through the main FastAPI app
if getenv("RUNTIME_ENV") == "dev" and getenv("START_PLAYGROUND_SERVER", "false").lower() == "true":
    try:
        from workspace.dev_resources import dev_fastapi
        playground.serve(f"http://localhost:{dev_fastapi.host_port}")
    except ImportError:
        # Fall back to port 8000 if dev_resources not available
        playground.serve("http://localhost:8000")

playground_router = playground.get_async_router()
